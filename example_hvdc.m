% Example to run AC/DC power flow with HVDC
% Define options
mdopt = macdcoption;
mdopt(13) = 1;     % enable output info

% Run AC/DC power flow with voltage slack control
[baseMVA, bus, gen, branch, busdc, convdc, branchdc, converged, timecalc] = ...
    runacdcpf('case5_stagg', 'case5_stagg_MTDCslack', mdopt);

% Display results
disp('AC/DC Power Flow Results:');
disp(['Converged: ', num2str(converged)]);
disp(['Calculation time: ', num2str(timecalc), ' seconds']);

% Display DC network results
disp('DC Network Results:');
disp(busdc);