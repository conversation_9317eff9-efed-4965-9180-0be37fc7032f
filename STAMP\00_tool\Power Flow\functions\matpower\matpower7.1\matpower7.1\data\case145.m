function mpc = case145
%CASE145    Power flow data for IEEE 145 bus, 50 generator dynamic test case.
%   See CASEFORMAT for details on the MATPOWER case file format.
%   Converted by MATPOWER 5.1 using CDF2MPC on 18-May-2016
%   from 'dd50cdf.txt'.
%   See end of file for warnings generated during conversion.
%
%   Converted from IEEE CDF file from:
%       https://labs.ece.uw.edu/pstca/, namely
%       https://labs.ece.uw.edu/pstca/dyn50/dd50cdf.txt
%
%   This is a solved power flow case. MATPOWER requires that the
%   'pf.enforce_q_lims' option be on to obtain the same solution.
%
%    01/02/90 IEEE WORKING GROUP    100.0 1990 S 50-GEN  CASE

%% MATPOWER Case Format : Version 2
mpc.version = '2';

%%-----  Power Flow Data  -----%%
%% system MVA base
mpc.baseMVA = 100;

%% bus data
%	bus_i	type	Pd	Qd	Gs	Bs	area	Vm	Va	baseKV	zone	Vmax	Vmin
mpc.bus = [
	1	1	0	0	0	0	2	1.081	-4.32	500	2	1.06	0.94;
	2	1	0	0	0	0	2	1.0809	-4.39	500	2	1.06	0.94;
	3	1	0	0	0	-126	2	1.1015	-4.02	25.7	2	1.06	0.94;
	4	1	0	0	0	-126	2	1.1015	-4.02	25.7	2	1.06	0.94;
	5	1	0	0	0	-126	2	1.1018	-4.02	25.7	2	1.06	0.94;
	6	1	0	0	0	0	2	1.0433	-7.84	500	2	1.06	0.94;
	7	1	0	0	0	0	2	1.0763	3.21	500	2	1.06	0.94;
	8	1	0	0	0	0	2	1.1137	1.15	100	2	1.06	0.94;
	9	1	0	0	0	0	2	1.0396	-8.05	500	2	1.06	0.94;
	10	1	0	0	0	0	2	1.0396	-8.05	500	2	1.06	0.94;
	11	1	0	0	0	0	2	1.0937	-10.66	100	2	1.06	0.94;
	12	1	0	0	0	0	2	1.0389	-8.77	500	2	1.06	0.94;
	13	1	0	0	0	0	2	1.0982	-11.43	100	2	1.06	0.94;
	14	1	0	0	0	0	2	1.0385	-9.18	500	2	1.06	0.94;
	15	1	0	0	0	-126	2	1.0683	-9.81	100	2	1.06	0.94;
	16	1	0	0	0	-126	2	1.0686	-9.86	100	2	1.06	0.94;
	17	1	0	0	0	-250	2	1.0012	-9.44	500	2	1.06	0.94;
	18	1	0	0	0	-126	2	1.0746	-10.88	100	2	1.06	0.94;
	19	1	0	0	0	-58	2	1.0708	-10.96	100	2	1.06	0.94;
	20	1	0	0	0	0	2	1.1131	-10.96	100	2	1.06	0.94;
	21	1	0	0	0	0	2	1.1086	-11.24	100	2	1.06	0.94;
	22	1	0	0	0	0	2	1.0311	-3.88	500	2	1.06	0.94;
	23	1	0	0	0	0	2	1.0979	-5.51	100	2	1.06	0.94;
	24	1	0	0	0	0	2	1.0272	2.3	500	2	1.06	0.94;
	25	1	0	0	0	0	2	1.038	-9.87	500	2	1.06	0.94;
	26	1	0	0	0	0	2	1.0894	-11.37	100	2	1.06	0.94;
	27	1	0	0	0	0	2	1.0389	-13.07	500	2	1.06	0.94;
	28	1	0	0	0	-126	2	1.0762	-15.28	100	2	1.06	0.94;
	29	1	0	0	0	-126	2	1.0746	-15.44	100	2	1.06	0.94;
	30	1	0	0	0	0	2	1.0731	-5.35	100	2	1.06	0.94;
	31	1	0	0	0	0	2	1.0905	-11.81	100	2	1.06	0.94;
	32	1	0	0	0	0	2	1.0937	-10.66	100	2	1.06	0.94;
	33	1	0	0	0	0	2	1.1392	-4.06	220	2	1.06	0.94;
	34	1	45.05	46.56	0	0	2	1.1387	-4	220	2	1.06	0.94;
	35	1	49.19	27.53	0	0	2	1.139	-4.08	220	2	1.06	0.94;
	36	1	0	0	0	0	2	1.1385	-3.82	220	2	1.06	0.94;
	37	1	0	0	0	0	2	1.1235	-6.23	220	2	1.06	0.94;
	38	1	0	0	0	0	2	1.1306	-5.29	220	2	1.06	0.94;
	39	1	0	0	0	0	2	1.127	-7.92	220	2	1.06	0.94;
	40	1	0	0	0	0	2	1.1269	-7.92	220	2	1.06	0.94;
	41	1	0	0	54	-10	2	1.1188	-10.43	100	2	1.06	0.94;
	42	1	0	0	54.8	-10	2	1.1188	-10.45	100	2	1.06	0.94;
	43	1	0	0	0	0	2	1.1189	-10.4	220	2	1.06	0.94;
	44	1	0	0	0	0	2	1.1189	-10.42	220	2	1.06	0.94;
	45	1	0	0	0	0	2	1.1173	-11.41	220	2	1.06	0.94;
	46	1	0	0	0	0	2	1.1173	-11.41	220	2	1.06	0.94;
	47	1	0	0	15.2	5.37	2	1.1275	-6.73	100	2	1.06	0.94;
	48	1	0	0	13.8	5.2	2	1.1278	-6.71	100	2	1.06	0.94;
	49	1	0	0	0	0	2	1.1279	-6.7	220	2	1.06	0.94;
	50	1	0	0	0	0	2	1.1276	-6.72	220	2	1.06	0.94;
	51	1	58.45	28.44	0	0	2	1.1124	-10.16	220	2	1.06	0.94;
	52	1	0	0	23	-9.7	2	1.1118	-11.13	100	2	1.06	0.94;
	53	1	0	0	23.2	-9.7	2	1.1118	-11.13	100	2	1.06	0.94;
	54	1	0	0	18.8	-9.3	2	1.1131	-11.79	100	2	1.06	0.94;
	55	1	0	0	18.7	-9.2	2	1.1131	-11.79	100	2	1.06	0.94;
	56	1	0	0	18.6	-7.5	2	1.1072	-9.94	100	2	1.06	0.94;
	57	1	0	0	18.8	-7	2	1.1072	-9.94	100	2	1.06	0.94;
	58	1	76.3	-10.8	121	140	2	1.1067	-9.76	100	2	1.06	0.94;
	59	1	0	0	508	8.63	2	1.1165	-10.84	100	2	1.06	0.94;
	60	2	0	0	201	165	2	1.137	-6.37	100	2	1.06	0.94;
	61	1	0	0	0	149	2	1.1144	-11.89	220	2	1.06	0.94;
	62	1	0	0	0	0	2	1.0566	-14.47	100	2	1.06	0.94;
	63	1	0	0	823	525	2	1.1109	-13.98	100	2	1.06	0.94;
	64	1	0	0	123	-26	2	1.098	-9.29	100	2	1.06	0.94;
	65	1	0	0	124	-26	2	1.098	-9.29	100	2	1.06	0.94;
	66	1	102.2	26.7	216	1897	2	1.1129	1.32	100	2	1.06	0.94;
	67	2	0	0	1821	1280	2	1.09	-5.66	100	2	1.06	0.94;
	68	1	0	-7.41	56.3	-25	2	1.2086	-30.99	100	2	1.06	0.94;
	69	1	0	0	971	-144	2	1.0968	-10.42	100	2	1.06	0.94;
	70	1	0	56.63	103	-86	2	0.9998	-14.17	100	2	1.06	0.94;
	71	1	0	-21.2	106	-122	2	1.0275	-14.26	100	2	1.06	0.94;
	72	1	0	0	1019	24.6	2	1.1007	-11.19	100	2	1.06	0.94;
	73	1	0	0	1222	841	2	1.0975	-11.06	100	2	1.06	0.94;
	74	1	81.9	43.7	857	574	2	1.0973	-11.46	100	2	1.06	0.94;
	75	1	0	0	387	199	2	1.1179	-15.19	100	2	1.06	0.94;
	76	1	0	0	-667	-185	2	1.0209	5.54	100	2	1.06	0.94;
	77	1	0	0	0	-125	2	0.988	6.72	100	2	1.06	0.94;
	78	1	89	26.8	0	0	2	1.074	-5.19	100	2	1.06	0.94;
	79	2	9.1	3	238	27.1	2	1.052	-9.51	100	2	1.06	0.94;
	80	2	17.1	5	-0.08	29.9	2	1.069	-8.21	100	2	1.06	0.94;
	81	1	82.2	-93.1	70	-47	2	1.1304	-25.86	100	2	1.06	0.94;
	82	2	2.1	1.1	111	-28	2	0.975	-18.66	100	2	1.06	0.94;
	83	1	0	0	105	-0.42	2	1.0985	-5.38	100	2	1.06	0.94;
	84	1	24.3	8.2	0	0	2	1.1156	-9.44	100	2	1.06	0.94;
	85	1	27.4	0.3	0	0	2	1.1165	-13.05	100	2	1.06	0.94;
	86	1	0	0	292	-22	2	1.0567	-14.01	100	2	1.06	0.94;
	87	1	0	0	37.7	-47	2	1.0652	-7.17	100	2	1.06	0.94;
	88	1	69	20.9	0	0	2	1.1094	-8.35	100	2	1.06	0.94;
	89	2	0.6	0.2	678	71.1	2	1.066	3.68	100	2	1.06	0.94;
	90	2	4.6	1.5	29.5	-19	2	0.95	-7.35	100	2	1.06	0.94;
	91	2	0	0	19.3	-27	2	1	-9.28	100	2	1.06	0.94;
	92	1	0	31.02	43.4	-43	2	0.9561	-12.75	100	2	1.06	0.94;
	93	2	100.4	73.2	0	0	2	1	-1.92	18.5	2	1.06	0.94;
	94	2	15.4	7.6	140	-141	2	1.02	-0.74	100	2	1.06	0.94;
	95	2	6.7	2.2	27.2	-58	2	0.92	18.88	100	2	1.06	0.94;
	96	2	0	0	47.8	-45	2	1	-8.98	100	2	1.06	0.94;
	97	2	0	0	129	-177	2	0.967	-4.34	100	2	1.06	0.94;
	98	2	0	0	82.1	-121	2	0.97	5.19	100	2	1.06	0.94;
	99	2	10.46	5.23	0	0	2	1	1.1	18	2	1.06	0.94;
	100	2	0	0	59.1	-101	2	1.014	0.7	100	2	1.06	0.94;
	101	2	17.8	4.5	64.7	-238	2	1.039	-6.09	100	2	1.06	0.94;
	102	2	37.6	9.2	458	-619	2	1.019	-4.76	100	2	1.06	0.94;
	103	2	0	0	62.4	-45	2	1	1.51	100	2	1.06	0.94;
	104	2	30.2	7.6	0	0	2	1.0059	13.68	100	2	1.06	0.94;
	105	2	96	167.4	24.2	-999	2	1.007	-2.8	100	2	1.06	0.94;
	106	2	64	16	36.9	-719	2	1.005	-2.75	100	2	1.06	0.94;
	107	1	-17.5	-12.8	28.8	-22	2	1.0211	-13.57	100	2	1.06	0.94;
	108	2	0	0	520	-244	2	1.014	-14.03	100	2	1.06	0.94;
	109	2	0	0	84.5	-47	2	0.915	-18.46	100	2	1.06	0.94;
	110	2	100.4	73.2	0	0	2	1	-1.31	18.5	2	1.06	0.94;
	111	2	60.4	1166	23.3	-999	2	1	7.97	100	2	1.06	0.94;
	112	2	18.6	4.6	64.3	-236	2	1.037	-6.26	100	2	1.06	0.94;
	113	1	0	0	0	0	2	0.978	-4.39	24	2	1.06	0.94;
	114	1	0	0	0	0	2	0.978	-4.39	24	2	1.06	0.94;
	115	2	683.5	184.7	2856	9.41	1	1.049	-15.61	100	1	1.06	0.94;
	116	2	792.6	315.5	1930	-195	1	1.043	-16.86	100	1	1.06	0.94;
	117	2	485.3	71.4	2410	-54	2	1.03	-15.32	100	2	1.06	0.94;
	118	2	651.9	328.4	4788	-979	2	1.01	-17.79	100	2	1.06	0.94;
	119	2	2094	3774	9999	-999	2	1.013	-59.41	100	2	1.06	0.94;
	120	1	-408	175.1	2026	855	2	1.0331	-51.6	100	2	1.06	0.94;
	121	2	237.7	-17.3	306	1055	2	1.046	-20.2	100	2	1.06	0.94;
	122	2	29.2	7	397	-61	2	1	-2.79	100	2	1.06	0.94;
	123	1	-84	-19	118	1.4	2	1.0171	-33.12	100	2	1.06	0.94;
	124	2	94.1	780.3	766	-999	2	1	-1.89	100	2	1.06	0.94;
	125	1	-712	-319	2831	-71	2	1.0084	-32.59	100	2	1.06	0.94;
	126	1	-333	-160	1604	347	2	1.0524	-73.9	100	2	1.06	0.94;
	127	1	-546	-72	305	-51	2	1.007	-36.4	100	2	1.06	0.94;
	128	2	4075	703.5	5252	-472	2	1.025	-39.71	100	2	1.06	0.94;
	129	1	-482	-122	3855	387	2	0.9802	-73.07	100	2	1.06	0.94;
	130	2	4328	944.3	3830	-91	1	1.057	-51.87	100	1	1.06	0.94;
	131	2	21840	4320	5145	-780	1	1.042	-24.32	100	1	1.06	0.94;
	132	2	491.9	110.2	1239	-226	1	1.042	-7.24	100	1	1.06	0.94;
	133	1	-83	-36.3	103	-5.9	2	1.0922	-11.6	100	2	1.06	0.94;
	134	2	22309	7402	-141	-78	1	1.044	-10.82	100	1	1.06	0.94;
	135	2	4298	1264	-999	835	1	1.107	29.04	100	1	1.06	0.94;
	136	2	52951	13552	-999	375	1	1.083	4.39	100	1	1.06	0.94;
	137	2	12946	2608	499	-219	1	1.064	-72.73	100	1	1.06	0.94;
	138	1	-363	-188	433	-81	2	1.1138	12.01	100	2	1.06	0.94;
	139	2	57718	13936	1609	-999	1	1.04	-10.56	100	1	1.06	0.94;
	140	2	24775	6676	-289	-77	1	1.05	-26.16	100	1	1.06	0.94;
	141	2	32799	11361	5212	-999	1	1.053	-9.12	100	1	1.06	0.94;
	142	2	17737	3934	4323	2210	1	1.155	-10.73	100	1	1.06	0.94;
	143	2	4672	1709	586	-999	1	1.031	-13.66	100	1	1.06	0.94;
	144	2	9602	2203	-436	-999	1	0.997	-8.58	100	1	1.06	0.94;
	145	3	9173	1555	-999	457	1	1.052	5.02	100	1	1.06	0.94;
];

%% generator data
%	bus	Pg	Qg	Qmax	Qmin	Vg	mBase	status	Pmax	Pmin	Pc1	Pc2	Qc1min	Qc1max	Qc2min	Qc2max	ramp_agc	ramp_10	ramp_30	ramp_q	apf
mpc.gen = [
	60	51	32.92	53.4	-20.4	1.137	100	1	151	0	0	0	0	0	0	0	0	0	0	0	0;
	67	1486	285.2	891.6	-594	1.09	100	1	1586	0	0	0	0	0	0	0	0	0	0	0	0;
	79	250.2	-15.95	150.1	-100	1.052	100	1	350.2	0	0	0	0	0	0	0	0	0	0	0	0;
	80	47	-15.06	28.2	-18.8	1.069	100	1	147	0	0	0	0	0	0	0	0	0	0	0	0;
	82	70	17.15	42	-28	0.975	100	1	170	0	0	0	0	0	0	0	0	0	0	0	0;
	89	673	136.39	403.8	-269	1.066	100	1	773	0	0	0	0	0	0	0	0	0	0	0	0;
	90	22	-3.87	13.2	-8.8	0.95	100	1	122	0	0	0	0	0	0	0	0	0	0	0	0;
	91	64	-1.54	38.4	-25.6	1	100	1	164	0	0	0	0	0	0	0	0	0	0	0	0;
	93	700	373.81	766	-520	1	100	1	800	0	0	0	0	0	0	0	0	0	0	0	0;
	94	300	19.05	288	-192	1.02	100	1	400	0	0	0	0	0	0	0	0	0	0	0	0;
	95	131	10.12	108.6	-72.4	0.92	100	1	231	0	0	0	0	0	0	0	0	0	0	0	0;
	96	60	21.11	36	-24	1	100	1	160	0	0	0	0	0	0	0	0	0	0	0	0;
	97	140	45.63	84	-56	0.967	100	1	240	0	0	0	0	0	0	0	0	0	0	0	0;
	98	426	-32.73	255.6	-170	0.97	100	1	526	0	0	0	0	0	0	0	0	0	0	0	0;
	99	200	-8.36	110	-110	1	100	1	300	0	0	0	0	0	0	0	0	0	0	0	0;
	100	170	58.72	102	-68	1.014	100	1	270	0	0	0	0	0	0	0	0	0	0	0	0;
	101	310.9	148.66	186.5	-124	1.039	100	1	410.9	0	0	0	0	0	0	0	0	0	0	0	0;
	102	2040	488.9	640	0	1.019	100	1	2140	0	0	0	0	0	0	0	0	0	0	0	0;
	103	135	4.96	81	-54	1	100	1	235	0	0	0	0	0	0	0	0	0	0	0	0;
	104	2000	500	500	0	1.045	100	1	2100	0	0	0	0	0	0	0	0	0	0	0	0;
	105	1620	388.34	1008	0	1.007	100	1	1720	0	0	0	0	0	0	0	0	0	0	0	0;
	106	1080	209.36	671	0	1.005	100	1	1180	0	0	0	0	0	0	0	0	0	0	0	0;
	108	800	77.28	480	-320	1.014	100	1	900	0	0	0	0	0	0	0	0	0	0	0	0;
	109	52	-15.55	31.2	-20.8	0.915	100	1	152	0	0	0	0	0	0	0	0	0	0	0	0;
	110	700	519.84	766	0	1	100	1	800	0	0	0	0	0	0	0	0	0	0	0	0;
	111	2000	563.72	1000	0	1	100	1	2100	0	0	0	0	0	0	0	0	0	0	0	0;
	112	300	140.11	160	0	1.037	100	1	400	0	0	0	0	0	0	0	0	0	0	0	0;
	115	2493	142.72	1496	-997	1.049	100	1	2593	0	0	0	0	0	0	0	0	0	0	0	0;
	116	2713	631.84	1628	-1085	1.043	100	1	2813	0	0	0	0	0	0	0	0	0	0	0	0;
	117	2627	258.54	1576	-1051	1.03	100	1	2727	0	0	0	0	0	0	0	0	0	0	0	0;
	118	4220	660.38	2532	-1688	1.01	100	1	4320	0	0	0	0	0	0	0	0	0	0	0	0;
	119	8954	4748.48	5373	-3582	1.013	100	1	9054	0	0	0	0	0	0	0	0	0	0	0	0;
	121	2997	-160.22	1798	-1199	1.046	100	1	3097	0	0	0	0	0	0	0	0	0	0	0	0;
	122	1009	174.04	605.4	-404	1	100	1	1109	0	0	0	0	0	0	0	0	0	0	0	0;
	124	3005	569.19	1803	-1202	1	100	1	3105	0	0	0	0	0	0	0	0	0	0	0	0;
	128	12963	2610.82	7778	-5185	1.025	100	1	13063	0	0	0	0	0	0	0	0	0	0	0	0;
	130	5937	1834.96	3562	-2375	1.057	100	1	6037	0	0	0	0	0	0	0	0	0	0	0	0;
	131	28300	7473.04	16980	-9999	1.042	100	1	28400	0	0	0	0	0	0	0	0	0	0	0	0;
	132	3095	633.42	1857	-1238	1.042	100	1	3195	0	0	0	0	0	0	0	0	0	0	0	0;
	134	20626	7402.14	12375	-8250	1.044	100	1	20726	0	0	0	0	0	0	0	0	0	0	0	0;
	135	5982	1564.84	3589	-2393	1.107	100	1	6082	0	0	0	0	0	0	0	0	0	0	0	0;
	136	51950	14453.5	31170	-9999	1.083	100	1	52050	0	0	0	0	0	0	0	0	0	0	0	0;
	137	12068	3450.76	7241	-4827	1.064	100	1	12168	0	0	0	0	0	0	0	0	0	0	0	0;
	139	56834	15849.65	34100	-9999	1.04	100	1	56934	0	0	0	0	0	0	0	0	0	0	0	0;
	140	23123	6710.47	13874	-9249	1.05	100	1	23223	0	0	0	0	0	0	0	0	0	0	0	0;
	141	37911	11669.52	22747	-9999	1.053	100	1	38011	0	0	0	0	0	0	0	0	0	0	0	0;
	142	24449	5496.12	14670	-9780	1.155	100	1	24549	0	0	0	0	0	0	0	0	0	0	0	0;
	143	5254	2158.63	3152	-2101	1.031	100	1	5354	0	0	0	0	0	0	0	0	0	0	0	0;
	144	11397	2686.85	6838	-4559	0.997	100	1	11497	0	0	0	0	0	0	0	0	0	0	0	0;
	145	14118.62	2987.15	9999	-9999	1.052	100	1	14218.62	0	0	0	0	0	0	0	0	0	0	0	0;
];

%% branch data
%	fbus	tbus	r	x	b	rateA	rateB	rateC	ratio	angle	status	angmin	angmax
mpc.branch = [
	1	2	3e-05	0.0008	0.0632	8659	0	0	0	0	1	-360	360;
	1	2	3e-05	0.0008	0.0632	0	0	0	0	0	1	-360	360;
	1	3	-0.009	-0.1718	0	0	0	0	0.935	0	1	-360	360;
	1	4	-0.009	-0.1718	0	0	0	0	0.935	0	1	-360	360;
	1	5	-0.0089	-0.1697	0	0	0	0	0.935	0	1	-360	360;
	1	6	0.00194	0.0209	2.3792	8659	0	0	0	0	1	-360	360;
	1	33	0.0001	0.006	0	750	0	0	0.935	0	1	-360	360;
	1	93	0.0002	0.0138	0	0	0	0	1.1036	0	1	-360	360;
	1	93	0.0002	0.0138	0	0	0	0	1.1036	0	1	-360	360;
	2	6	0.00194	0.0209	2.3792	8659	0	0	0	0	1	-360	360;
	2	113	0	0.0148	0	0	0	0	1.1052	0	1	-360	360;
	2	114	0.00018	0.0145	0	0	0	0	1.1052	0	1	-360	360;
	3	33	0.0002	0.0221	0	0	0	0	0	0	1	-360	360;
	4	33	0.0002	0.0221	0	0	0	0	0	0	1	-360	360;
	5	33	0.0002	0.0219	0	0	0	0	0	0	1	-360	360;
	6	7	0.00129	0.0139	1.4652	8659	0	0	0	0	1	-360	360;
	6	9	0.00016	0.0017	0.1752	8659	0	0	0	0	1	-360	360;
	6	10	0.00016	0.0017	0.1752	8659	0	0	0	0	1	-360	360;
	6	12	0.0002	0.0021	0.8776	8659	0	0	0	0	1	-360	360;
	6	12	0.0002	0.0021	0.8776	8659	0	0	0	0	1	-360	360;
	7	8	-0.0112	-0.1516	0	0	0	0	0.9716	0	1	-360	360;
	7	66	0.00015	0.0097	0	750	0	0	0.9716	0	1	-360	360;
	7	104	0.00036	0.019	0	0	0	0	1.1052	0	1	-360	360;
	7	104	0.00041	0.0174	0	0	0	0	1.1052	0	1	-360	360;
	8	66	0.0002	0.0299	0	0	0	0	0	0	1	-360	360;
	8	66	0.0002	0.0221	0	0	0	0	0	0	1	-360	360;
	9	11	-0.0217	-0.3062	0	0	0	0	0.9166	0	1	-360	360;
	9	69	0.0004	0.0188	0	750	0	0	0.9166	0	1	-360	360;
	10	32	-0.027	-0.3041	0	0	0	0	0.9166	0	1	-360	360;
	10	69	0.0004	0.0187	0	750	0	0	0.9166	0	1	-360	360;
	11	69	0.0002	0.0262	0	0	0	0	0	0	1	-360	360;
	12	13	-0.0223	-0.3099	0	0	0	0	0.9166	0	1	-360	360;
	12	13	-0.0237	-0.316	0	0	0	0	0.9166	0	1	-360	360;
	12	13	-0.0237	-0.316	0	0	0	0	0.9166	0	1	-360	360;
	12	14	0.00096	0.0091	0.8556	8659	0	0	0	0	1	-360	360;
	12	14	0.00096	0.0091	0.8556	8659	0	0	0	0	1	-360	360;
	12	25	0.00051	0.0055	0.625	8659	0	0	0	0	1	-360	360;
	12	25	0.00051	0.0055	0.625	8659	0	0	0	0	1	-360	360;
	12	72	0.0003	0.0189	0	750	0	0	0.9166	0	1	-360	360;
	12	72	0.0003	0.019	0	750	0	0	0.9166	0	1	-360	360;
	12	72	0.0003	0.019	0	0	0	0	0.9166	0	1	-360	360;
	13	72	0.0002	0.026	0	0	0	0	0	0	1	-360	360;
	13	72	0.0003	0.0262	0	0	0	0	0	0	1	-360	360;
	13	72	0.0002	0.026	0	0	0	0	0	0	1	-360	360;
	14	15	-0.0415	-0.3996	0	0	0	0	0.9164	0	1	-360	360;
	14	16	-0.01	-0.1669	0	0	0	0	0.9164	0	1	-360	360;
	14	17	0.00339	0.0367	3.4582	8659	0	0	0	0	1	-360	360;
	14	17	0.00352	0.0367	3.4516	8659	0	0	0	0	1	-360	360;
	14	58	0.0002	0.0097	0	750	0	0	0.9164	0	1	-360	360;
	15	58	0.0002	0.0255	0	0	0	0	0	0	1	-360	360;
	16	58	0.0002	0.022	0	0	0	0	0	0	1	-360	360;
	17	18	-0.3181	-1.315	0	0	0	0	0.8708	0	1	-360	360;
	17	19	0	-0.847	0	0	0	0	0.8634	0	1	-360	360;
	17	20	0	-0.8676	0	0	0	0	0.8634	0	1	-360	360;
	17	21	-0.0095	-0.1615	0	0	0	0	0.8708	0	1	-360	360;
	17	22	0.00228	0.0276	2.6204	8659	0	0	0	0	1	-360	360;
	17	59	0.0001	0.0071	0	750	0	0	0.8708	0	1	-360	360;
	18	59	0.0002	0.0298	0	0	0	0	0	0	1	-360	360;
	19	59	0	0.0629	0	0	0	0	0	0	1	-360	360;
	20	59	0	0.0638	0	0	0	0	0	0	1	-360	360;
	21	59	0.0002	0.0329	0	0	0	0	0	0	1	-360	360;
	22	23	0	-0.3787	0	0	0	0	0.9322	0	1	-360	360;
	22	24	0.00173	0.0208	1.9648	8659	0	0	0	0	1	-360	360;
	22	30	0	-0.3066	0	0	0	0	0.9532	0	1	-360	360;
	22	78	0	0.0268	0	0	0	0	0.9532	0	1	-360	360;
	22	83	0	0.0349	0	0	0	0	0.9322	0	1	-360	360;
	23	83	0.0004	0.0595	0	0	0	0	0	0	1	-360	360;
	23	83	0.0003	0.0597	0	0	0	0	0	0	1	-360	360;
	24	76	0.0002	0.0088	0	0	0	0	0.9898	0	1	-360	360;
	24	77	-0.0023	-0.0603	0	0	0	0	0.9898	0	1	-360	360;
	25	26	-0.006	-0.1375	0	0	0	0	0.9166	0	1	-360	360;
	25	27	0.0023	0.0266	3.0508	8659	0	0	0	0	1	-360	360;
	25	27	0.0023	0.0266	3.0508	8659	0	0	0	0	1	-360	360;
	25	31	-0.0082	-0.1648	0	0	0	0	0.9166	0	1	-360	360;
	25	73	0.0003	0.0172	0	750	0	0	0.9166	0	1	-360	360;
	25	74	0.0004	0.0179	0	750	0	0	0.9166	0	1	-360	360;
	26	73	0.0003	0.0267	0	0	0	0	0	0	1	-360	360;
	27	28	-0.1153	-0.7453	0	0	0	0	0.9074	0	1	-360	360;
	27	29	-0.0163	-0.2618	0	0	0	0	0.9074	0	1	-360	360;
	27	75	0.00016	0.01	0	750	0	0	0.9074	0	1	-360	360;
	28	75	0.0002	0.029	0	0	0	0	0	0	1	-360	360;
	29	75	0.0002	0.0269	0	0	0	0	0	0	1	-360	360;
	30	78	0	0.0335	0	0	0	0	0	0	1	-360	360;
	31	74	0.0003	0.0279	0	0	0	0	0	0	1	-360	360;
	32	69	0.0002	0.0265	0	0	0	0	0	0	1	-360	360;
	33	34	6e-05	0.0009	0.0006	788	0	0	0	0	1	-360	360;
	33	35	6e-05	0.0009	0.0006	490	0	0	0	0	1	-360	360;
	33	37	0.00996	0.0707	0.1116	0	0	0	0	0	1	-360	360;
	33	38	0.00995	0.0693	0.111	1155	0	0	0	0	1	-360	360;
	33	39	0.0085	0.0699	0.1006	1208	0	0	0	0	1	-360	360;
	33	40	0.00849	0.0698	0.1004	1208	0	0	0	0	1	-360	360;
	33	49	0.0056	0.0493	0.0778	1078	0	0	0	0	1	-360	360;
	33	50	0.0056	0.0493	0.0778	1078	0	0	0	0	1	-360	360;
	33	110	0.00024	0.0157	0	0	0	0	1.18	0	1	-360	360;
	33	110	0.00023	0.0156	0	0	0	0	1.18	0	1	-360	360;
	34	36	0.00025	0.0022	0.0006	788	0	0	0	0	1	-360	360;
	36	99	0.0008	0.0455	0	0	0	0	1.1291	0	1	-360	360;
	37	87	0.00093	0.0442	0	0	0	0	1.05	0	1	-360	360;
	37	88	0.0031	0.1651	0	0	0	0	0	0	1	-360	360;
	38	88	0.0031	0.1638	0	0	0	0	0	0	1	-360	360;
	39	43	0.00602	0.0495	0.0712	1208	0	0	0	0	1	-360	360;
	39	84	0.00722	0.2786	0	0	0	0	0	0	1	-360	360;
	40	44	0.00603	0.0496	0.0714	1208	0	0	0	0	1	-360	360;
	40	84	0.00729	0.2756	0	0	0	0	0	0	1	-360	360;
	41	42	0.0005	0.1514	0	0	0	0	0	0	1	-360	360;
	41	43	1e-05	0.0009	0.0006	0	0	0	0	0	1	-360	360;
	42	44	1e-05	0.0009	0.0006	0	0	0	0	0	1	-360	360;
	43	46	0.00618	0.0508	0.0732	1208	0	0	0	0	1	-360	360;
	44	45	0.00618	0.0508	0.0732	1208	0	0	0	0	1	-360	360;
	45	61	0.00445	0.0366	0.0526	1208	0	0	0	0	1	-360	360;
	45	85	0	0.26	0	0	0	0	0	0	1	-360	360;
	46	61	0.00445	0.0366	0.0526	1208	0	0	0	0	1	-360	360;
	46	85	0	0.2592	0	0	0	0	0	0	1	-360	360;
	47	48	-0.01	0.2306	0	0	0	0	0	0	1	-360	360;
	47	50	1e-05	0.0009	0.0006	0	0	0	0	0	1	-360	360;
	47	87	0.0831	0.401	0	0	0	0	0	0	1	-360	360;
	48	49	1e-05	0.0009	0.0006	0	0	0	0	0	1	-360	360;
	48	87	0.0998	0.436	0	0	0	0	0	0	1	-360	360;
	49	51	0.00898	0.079	0.1248	1078	0	0	0	0	1	-360	360;
	50	51	0.00898	0.079	0.1248	1078	0	0	0	0	1	-360	360;
	51	52	0.0029	0.0279	0.0466	858	0	0	0	0	1	-360	360;
	51	53	0.0029	0.0279	0.0466	858	0	0	0	0	1	-360	360;
	51	56	0.00759	0.0483	0.0712	1093	0	0	0	0	1	-360	360;
	51	57	0.00759	0.0483	0.0712	1093	0	0	0	0	1	-360	360;
	52	53	-0.0067	0.3911	0	0	0	0	0	0	1	-360	360;
	52	54	0.0047	0.0293	0.0462	0	0	0	0	0	1	-360	360;
	53	55	0.0047	0.0293	0.0462	0	0	0	0	0	1	-360	360;
	54	55	-0.0553	0.9289	0	0	0	0	0	0	1	-360	360;
	54	61	0.00141	0.0087	0.0138	390	0	0	0	0	1	-360	360;
	55	61	0.00141	0.0087	0.0138	390	0	0	0	0	1	-360	360;
	56	57	-0.009	0.3895	0	0	0	0	0	0	1	-360	360;
	56	58	0.0019	0.012	0.0178	0	0	0	0	0	1	-360	360;
	57	58	0.0019	0.012	0.0178	0	0	0	0	0	1	-360	360;
	58	59	0.6674	2.2175	0	0	0	0	0	0	1	-360	360;
	58	72	0.0302	0.2364	0	0	0	0	0	0	1	-360	360;
	58	87	0.0863	0.3906	0	0	0	0	0	0	1	-360	360;
	58	98	0.0131	0.1765	0	0	0	0	0	0	1	-360	360;
	58	100	0.1193	1.269	0	0	0	0	0	0	1	-360	360;
	58	103	0.8416	5.5383	0	0	0	0	0	0	1	-360	360;
	59	60	-0.1803	5.9659	0	0	0	0	0	0	1	-360	360;
	59	72	0.8613	3.0485	0	0	0	0	0	0	1	-360	360;
	59	79	0.0099	0.2644	0	0	0	0	0	0	1	-360	360;
	59	80	0.2876	2.3898	0	0	0	0	0	0	1	-360	360;
	59	89	0.3421	9.0571	0	0	0	0	0	0	1	-360	360;
	59	92	-0.007	0.5678	0	0	0	0	0	0	1	-360	360;
	59	94	0.7041	5.9885	0	0	0	0	0	0	1	-360	360;
	59	98	0.106	0.5845	0	0	0	0	0	0	1	-360	360;
	59	100	0.0183	0.2016	0	0	0	0	0	0	1	-360	360;
	59	103	0.0368	0.3341	0	0	0	0	0	0	1	-360	360;
	59	107	0.0372	0.8834	0	0	0	0	0	0	1	-360	360;
	60	135	-1.831	9.7964	0	0	0	0	0	0	1	-360	360;
	60	79	-0.0375	1.1068	0	0	0	0	0	0	1	-360	360;
	60	80	0.0655	2.6441	0	0	0	0	0	0	1	-360	360;
	60	90	-0.0201	1.5135	0	0	0	0	0	0	1	-360	360;
	60	92	-0.264	3.7139	0	0	0	0	0	0	1	-360	360;
	60	94	0.0012	0.0775	0	0	0	0	0	0	1	-360	360;
	60	95	-0.0855	0.9926	0	0	0	0	0	0	1	-360	360;
	60	138	-0.3639	1.7936	0	0	0	0	0	0	1	-360	360;
	61	62	-0.0362	-0.2608	0	0	0	0	1.05	0	1	-360	360;
	61	62	-0.0472	-0.5438	0	0	0	0	1.05	0	1	-360	360;
	61	63	0.00812	0.0782	0.1318	1331	0	0	0	0	1	-360	360;
	61	63	0.00812	0.0782	0.1318	1331	0	0	0	0	1	-360	360;
	61	64	0.00242	0.0318	0.0568	1839	0	0	0	0	1	-360	360;
	61	65	0.00242	0.0318	0.0568	1208	0	0	0	0	1	-360	360;
	61	86	0.00132	0.032	0	215	0	0	1.05	0	1	-360	360;
	61	86	0.0011	0.037	0	250	0	0	1.05	0	1	-360	360;
	61	86	0.0011	0.037	0	225	0	0	1.05	0	1	-360	360;
	62	86	0.0036	0.0501	0	0	0	0	0	0	1	-360	360;
	62	86	0.0013	0.0838	0	0	0	0	0	0	1	-360	360;
	63	64	0.0147	0.2825	0	0	0	0	0	0	1	-360	360;
	63	65	0.0147	0.2813	0	0	0	0	0	0	1	-360	360;
	63	66	0.0056	0.09	0	0	0	0	0	0	1	-360	360;
	63	67	0.0321	0.2785	0	0	0	0	0	0	1	-360	360;
	63	69	0.0107	0.1571	0	0	0	0	0	0	1	-360	360;
	63	102	0.0106	0.1583	0	0	0	0	0	0	1	-360	360;
	63	102	0.0106	0.1576	0	0	0	0	0	0	1	-360	360;
	63	102	0.0107	0.1604	0	0	0	0	0	0	1	-360	360;
	63	102	0.0104	0.1542	0	0	0	0	0	0	1	-360	360;
	63	116	-0.3897	6.8588	0	0	0	0	0	0	1	-360	360;
	63	117	0.003	0.056	0	0	0	0	0	0	1	-360	360;
	63	118	-0.0125	0.2425	0	0	0	0	0	0	1	-360	360;
	63	124	-0.1265	2.022	0	0	0	0	0	0	1	-360	360;
	64	65	0.0013	0.1674	0	0	0	0	0	0	1	-360	360;
	64	66	0.0039	0.0684	0	0	0	0	0	0	1	-360	360;
	64	67	0.0233	0.212	0	0	0	0	0	0	1	-360	360;
	64	69	0.0075	0.1196	0	0	0	0	0	0	1	-360	360;
	64	97	-0.4336	8.2923	0	0	0	0	0	0	1	-360	360;
	64	124	-0.1041	1.5375	0	0	0	0	0	0	1	-360	360;
	65	66	0.0039	0.0682	0	0	0	0	0	0	1	-360	360;
	65	67	0.0233	0.2111	0	0	0	0	0	0	1	-360	360;
	65	69	0.0075	0.1191	0	0	0	0	0	0	1	-360	360;
	65	97	-0.4292	8.2582	0	0	0	0	0	0	1	-360	360;
	65	124	-0.1032	1.5312	0	0	0	0	0	0	1	-360	360;
	66	67	0.0081	0.0675	0	0	0	0	0	0	1	-360	360;
	66	68	-2.473	2.472	0	0	0	0	0	0	1	-360	360;
	66	69	0.0028	0.0381	0	0	0	0	0	0	1	-360	360;
	66	97	-0.1119	2.6432	0	0	0	0	0	0	1	-360	360;
	66	111	0	0.0264	0	0	0	0	0	0	1	-360	360;
	66	111	0.00057	0.0266	0	0	0	0	0	0	1	-360	360;
	66	111	0	0.0273	0	0	0	0	0	0	1	-360	360;
	66	111	0.00057	0.0264	0	0	0	0	0	0	1	-360	360;
	66	124	-0.0283	0.4902	0	0	0	0	0	0	1	-360	360;
	67	68	-3.443	3.7172	0	0	0	0	0	0	1	-360	360;
	67	69	0.0061	0.055	0	0	0	0	0	0	1	-360	360;
	67	97	0.0063	0.1166	0	0	0	0	0	0	1	-360	360;
	67	119	-0.2213	9.3918	0	0	0	0	0	0	1	-360	360;
	67	120	-0.0034	1.7847	0	0	0	0	0	0	1	-360	360;
	67	121	0.0082	1.17	0	0	0	0	0	0	1	-360	360;
	67	122	-0.0047	0.4473	0	0	0	0	0	0	1	-360	360;
	67	124	0.0003	0.0065	0	0	0	0	0	0	1	-360	360;
	67	125	0.0062	0.2519	0	0	0	0	0	0	1	-360	360;
	67	132	-0.3194	4.3566	0	0	0	0	0	0	1	-360	360;
	68	69	-0.692	0.6984	0	0	0	0	0	0	1	-360	360;
	69	70	0.0085	0.3333	0	0	0	0	0	0	1	-360	360;
	69	71	0.0075	0.312	0	0	0	0	0	0	1	-360	360;
	69	72	0.0013	0.01	0	0	0	0	0	0	1	-360	360;
	69	73	0.0098	0.0747	0	0	0	0	0	0	1	-360	360;
	69	74	0.0135	0.0741	0	0	0	0	0	0	1	-360	360;
	69	97	-0.0674	1.5849	0	0	0	0	0	0	1	-360	360;
	69	101	0.0174	0.2188	0	0	0	0	0	0	1	-360	360;
	69	112	0.0175	0.2201	0	0	0	0	0	0	1	-360	360;
	69	124	-0.0267	0.3986	0	0	0	0	0	0	1	-360	360;
	70	71	-0.4891	2.6613	0	0	0	0	0	0	1	-360	360;
	70	72	-0.0062	0.1216	0	0	0	0	0	0	1	-360	360;
	70	73	-0.0424	0.9125	0	0	0	0	0	0	1	-360	360;
	70	74	0.0032	0.9138	0	0	0	0	0	0	1	-360	360;
	70	101	-0.1248	1.0409	0	0	0	0	0	0	1	-360	360;
	70	112	-0.1257	1.0471	0	0	0	0	0	0	1	-360	360;
	71	72	-0.006	0.1138	0	0	0	0	0	0	1	-360	360;
	71	73	-0.0409	0.8541	0	0	0	0	0	0	1	-360	360;
	71	74	0.0018	0.8553	0	0	0	0	0	0	1	-360	360;
	71	101	-0.1592	1.2303	0	0	0	0	0	0	1	-360	360;
	71	112	-0.1603	1.2377	0	0	0	0	0	0	1	-360	360;
	72	73	0.0015	0.0275	0	0	0	0	0	0	1	-360	360;
	72	74	0.0028	0.0274	0	0	0	0	0	0	1	-360	360;
	72	98	0.0138	0.2417	0	0	0	0	0	0	1	-360	360;
	72	100	0.1337	1.7384	0	0	0	0	0	0	1	-360	360;
	72	101	0.0002	0.0802	0	0	0	0	0	0	1	-360	360;
	72	103	1.0224	7.5945	0	0	0	0	0	0	1	-360	360;
	72	112	0.0002	0.0806	0	0	0	0	0	0	1	-360	360;
	73	74	-0.0007	0.0393	0	0	0	0	0	0	1	-360	360;
	73	75	0.0147	0.2581	0	0	0	0	0	0	1	-360	360;
	73	81	-0.0122	0.3068	0	0	0	0	0	0	1	-360	360;
	73	82	0.0036	2.0169	0	0	0	0	0	0	1	-360	360;
	73	91	0.0271	0.5732	0	0	0	0	0	0	1	-360	360;
	73	96	0.0245	0.4805	0	0	0	0	0	0	1	-360	360;
	73	101	0.0044	0.6014	0	0	0	0	0	0	1	-360	360;
	73	105	0.0007	0.0325	0	0	0	0	0	0	1	-360	360;
	73	105	0.0007	0.0325	0	0	0	0	0	0	1	-360	360;
	73	105	0.0006	0.0295	0	0	0	0	0	0	1	-360	360;
	73	108	-0.0182	0.5832	0	0	0	0	0	0	1	-360	360;
	73	109	0.0524	3.0059	0	0	0	0	0	0	1	-360	360;
	73	112	0.0043	0.605	0	0	0	0	0	0	1	-360	360;
	73	121	-0.0268	1.7653	0	0	0	0	0	0	1	-360	360;
	74	75	0.0215	0.3277	0	0	0	0	0	0	1	-360	360;
	74	81	-0.0333	0.4631	0	0	0	0	0	0	1	-360	360;
	74	82	-0.0098	1.9859	0	0	0	0	0	0	1	-360	360;
	74	91	0.0413	0.7511	0	0	0	0	0	0	1	-360	360;
	74	96	0.435	7.6901	0	0	0	0	0	0	1	-360	360;
	74	101	0.0344	0.6005	0	0	0	0	0	0	1	-360	360;
	74	106	0.003	0.0335	0	0	0	0	0	0	1	-360	360;
	74	106	0.0005	0.0328	0	0	0	0	0	0	1	-360	360;
	74	108	-0.0187	0.4544	0	0	0	0	0	0	1	-360	360;
	74	109	0.1004	3.4697	0	0	0	0	0	0	1	-360	360;
	74	112	0.0345	0.6042	0	0	0	0	0	0	1	-360	360;
	74	121	-0.0348	1.3757	0	0	0	0	0	0	1	-360	360;
	75	82	0.0777	1.125	0	0	0	0	0	0	1	-360	360;
	75	91	-0.2255	3.1442	0	0	0	0	0	0	1	-360	360;
	75	96	-0.4516	4.631	0	0	0	0	0	0	1	-360	360;
	75	108	0.0042	0.1049	0	0	0	0	0	0	1	-360	360;
	75	109	0.1046	1.4465	0	0	0	0	0	0	1	-360	360;
	75	121	0.0178	0.3172	0	0	0	0	0	0	1	-360	360;
	76	77	0.0002	0.016	0	0	0	0	0	0	1	-360	360;
	76	89	0.0011	0.0221	0	0	0	0	0	0	1	-360	360;
	79	80	0.044	0.0991	0	0	0	0	0	0	1	-360	360;
	79	90	0.0506	2.471	0	0	0	0	0	0	1	-360	360;
	79	92	0.0017	0.3032	0	0	0	0	0	0	1	-360	360;
	79	94	0.1275	1.1195	0	0	0	0	0	0	1	-360	360;
	79	95	0.305	6.4154	0	0	0	0	0	0	1	-360	360;
	79	107	0.0786	1.414	0	0	0	0	0	0	1	-360	360;
	80	90	0.4658	5.8756	0	0	0	0	0	0	1	-360	360;
	80	92	0.1192	1.5053	0	0	0	0	0	0	1	-360	360;
	80	94	0.46	2.6475	0	0	0	0	0	0	1	-360	360;
	82	91	-0.2349	2.4188	0	0	0	0	0	0	1	-360	360;
	82	108	-0.0742	0.7278	0	0	0	0	0	0	1	-360	360;
	82	109	-0.0071	0.2634	0	0	0	0	0	0	1	-360	360;
	82	121	-0.1892	2.2054	0	0	0	0	0	0	1	-360	360;
	83	89	0.0582	0.3855	0	0	0	0	0	0	1	-360	360;
	89	103	-1.073	4.1433	0	0	0	0	0	0	1	-360	360;
	90	92	-0.138	8.2959	0	0	0	0	0	0	1	-360	360;
	90	94	0.0689	1.0717	0	0	0	0	0	0	1	-360	360;
	91	96	-0.1224	4.2463	0	0	0	0	0	0	1	-360	360;
	91	108	-0.1078	0.6994	0	0	0	0	0	0	1	-360	360;
	91	109	-0.2699	4.2634	0	0	0	0	0	0	1	-360	360;
	91	121	-0.2924	2.121	0	0	0	0	0	0	1	-360	360;
	92	94	0.2883	3.7717	0	0	0	0	0	0	1	-360	360;
	92	107	0.0176	3.0227	0	0	0	0	0	0	1	-360	360;
	94	95	0.0534	0.996	0	0	0	0	0	0	1	-360	360;
	94	138	-0.1125	1.8385	0	0	0	0	0	0	1	-360	360;
	95	138	-0.0732	0.6389	0	0	0	0	0	0	1	-360	360;
	96	108	-0.8215	6.1143	0	0	0	0	0	0	1	-360	360;
	97	124	-0.3793	1.9557	0	0	0	0	0	0	1	-360	360;
	98	100	-0.0063	0.3269	0	0	0	0	0	0	1	-360	360;
	98	103	0.0544	1.4358	0	0	0	0	0	0	1	-360	360;
	100	103	-0.0249	0.4891	0	0	0	0	0	0	1	-360	360;
	101	112	-0.0138	0.361	0	0	0	0	0	0	1	-360	360;
	102	117	-0.0003	0.019	0	0	0	0	0	0	1	-360	360;
	102	118	-0.0267	0.3222	0	0	0	0	0	0	1	-360	360;
	108	109	-0.0825	1.2713	0	0	0	0	0	0	1	-360	360;
	108	121	-0.0009	0.0431	0	0	0	0	0	0	1	-360	360;
	109	121	-0.1881	3.8499	0	0	0	0	0	0	1	-360	360;
	115	116	0.0008	0.0291	0	0	0	0	0	0	1	-360	360;
	115	117	-0.0092	0.2222	0	0	0	0	0	0	1	-360	360;
	115	118	-0.0044	0.0677	0	0	0	0	0	0	1	-360	360;
	115	143	-0.1017	0.4924	0	0	0	0	0	0	1	-360	360;
	116	117	0.00191	0.0288	0	0	0	0	0	0	1	-360	360;
	116	118	-0.001	0.044	0	0	0	0	0	0	1	-360	360;
	116	143	-0.2187	1.2896	0	0	0	0	0	0	1	-360	360;
	117	118	0.0008	0.0081	0	0	0	0	0	0	1	-360	360;
	117	143	-0.0834	0.6854	0	0	0	0	0	0	1	-360	360;
	118	131	-0.8925	6.2385	0	0	0	0	0	0	1	-360	360;
	118	132	-0.6967	8.143	0	0	0	0	0	0	1	-360	360;
	118	143	-0.0011	0.0231	0	0	0	0	0	0	1	-360	360;
	119	120	0.001	0.0236	0	0	0	0	0	0	1	-360	360;
	119	121	-0.011	0.2901	0	0	0	0	0	0	1	-360	360;
	119	122	-0.6013	5.8941	0	0	0	0	0	0	1	-360	360;
	119	124	-0.2618	3.394	0	0	0	0	0	0	1	-360	360;
	119	125	-0.0082	0.2595	0	0	0	0	0	0	1	-360	360;
	119	126	0.00153	0.0179	0	0	0	0	0	0	1	-360	360;
	119	127	-0.1172	1.3932	0	0	0	0	0	0	1	-360	360;
	119	128	-0.0054	0.0516	0	0	0	0	0	0	1	-360	360;
	119	129	0.0034	0.0642	0	0	0	0	0	0	1	-360	360;
	119	130	-0.0022	0.0163	0	0	0	0	0	0	1	-360	360;
	119	131	-0.0044	0.0242	0	0	0	0	0	0	1	-360	360;
	119	132	-0.4137	2.4027	0	0	0	0	0	0	1	-360	360;
	119	144	-0.8511	3.8358	0	0	0	0	0	0	1	-360	360;
	120	121	0.0009	0.0779	0	0	0	0	0	0	1	-360	360;
	120	122	-0.061	0.9305	0	0	0	0	0	0	1	-360	360;
	120	123	-0.0466	0.5011	0	0	0	0	0	0	1	-360	360;
	120	124	-0.0259	0.4722	0	0	0	0	0	0	1	-360	360;
	120	125	-0.0002	0.0555	0	0	0	0	0	0	1	-360	360;
	120	127	0.002	0.1818	0	0	0	0	0	0	1	-360	360;
	120	128	-0.0029	0.0743	0	0	0	0	0	0	1	-360	360;
	120	129	-0.0229	0.4911	0	0	0	0	0	0	1	-360	360;
	120	130	-0.1674	1.0675	0	0	0	0	0	0	1	-360	360;
	120	131	-0.0687	0.4516	0	0	0	0	0	0	1	-360	360;
	120	132	-0.0255	0.4566	0	0	0	0	0	0	1	-360	360;
	121	122	-0.0108	0.483	0	0	0	0	0	0	1	-360	360;
	121	123	-0.1712	1.9482	0	0	0	0	0	0	1	-360	360;
	121	124	-0.006	0.3494	0	0	0	0	0	0	1	-360	360;
	121	125	0	0.0124	0	0	0	0	0	0	1	-360	360;
	121	127	-0.0204	0.8338	0	0	0	0	0	0	1	-360	360;
	121	128	-0.0278	0.3095	0	0	0	0	0	0	1	-360	360;
	121	129	-0.4545	4.254	0	0	0	0	0	0	1	-360	360;
	121	131	-0.2183	1.5066	0	0	0	0	0	0	1	-360	360;
	121	132	-0.1308	1.3815	0	0	0	0	0	0	1	-360	360;
	122	123	-0.584	4.8609	0	0	0	0	0	0	1	-360	360;
	122	124	-0.0009	0.0552	0	0	0	0	0	0	1	-360	360;
	122	125	-0.0069	0.1583	0	0	0	0	0	0	1	-360	360;
	122	131	-0.2433	1.935	0	0	0	0	0	0	1	-360	360;
	122	132	-0.0187	0.2572	0	0	0	0	0	0	1	-360	360;
	122	133	-0.098	0.9821	0	0	0	0	0	0	1	-360	360;
	122	143	-0.0312	0.4888	0	0	0	0	0	0	1	-360	360;
	123	124	-0.223	1.967	0	0	0	0	0	0	1	-360	360;
	123	125	-0.0821	0.6062	0	0	0	0	0	0	1	-360	360;
	123	131	-0.1783	1.2535	0	0	0	0	0	0	1	-360	360;
	123	132	-0.1355	1.2041	0	0	0	0	0	0	1	-360	360;
	124	125	-0.0017	0.0949	0	0	0	0	0	0	1	-360	360;
	124	128	-1.153	8.2513	0	0	0	0	0	0	1	-360	360;
	124	131	-0.1062	0.8185	0	0	0	0	0	0	1	-360	360;
	124	132	-0.0094	0.1612	0	0	0	0	0	0	1	-360	360;
	124	133	-0.0342	1.1798	0	0	0	0	0	0	1	-360	360;
	124	143	-0.0078	0.7607	0	0	0	0	0	0	1	-360	360;
	125	127	-0.0791	0.9851	0	0	0	0	0	0	1	-360	360;
	125	128	-0.062	0.5991	0	0	0	0	0	0	1	-360	360;
	125	129	-0.4217	3.9702	0	0	0	0	0	0	1	-360	360;
	125	130	-1.974	8.4854	0	0	0	0	0	0	1	-360	360;
	125	131	-0.1251	0.6939	0	0	0	0	0	0	1	-360	360;
	125	132	-0.0536	0.5086	0	0	0	0	0	0	1	-360	360;
	127	128	-0.0026	0.124	0	0	0	0	0	0	1	-360	360;
	127	129	-0.0392	1.1082	0	0	0	0	0	0	1	-360	360;
	128	129	-0.001	0.0207	0	0	0	0	0	0	1	-360	360;
	128	130	-1.1	2.9924	0	0	0	0	0	0	1	-360	360;
	128	131	-1.559	4.0869	0	0	0	0	0	0	1	-360	360;
	130	131	-0.0027	0.0154	0	0	0	0	0	0	1	-360	360;
	130	132	-0.6509	3.031	0	0	0	0	0	0	1	-360	360;
	130	144	-0.7532	3.0664	0	0	0	0	0	0	1	-360	360;
	131	132	-0.0032	0.0411	0	0	0	0	0	0	1	-360	360;
	131	133	-1.077	5.5285	0	0	0	0	0	0	1	-360	360;
	131	143	-0.0588	0.4055	0	0	0	0	0	0	1	-360	360;
	131	144	-0.0022	0.0151	0	0	0	0	0	0	1	-360	360;
	132	133	-0.0916	0.8229	0	0	0	0	0	0	1	-360	360;
	132	143	-0.0049	0.0965	0	0	0	0	0	0	1	-360	360;
	132	144	-0.1108	0.9827	0	0	0	0	0	0	1	-360	360;
	133	143	-0.36	2.6309	0	0	0	0	0	0	1	-360	360;
	134	131	-0.4042	0.9144	0	0	0	0	0	0	1	-360	360;
	134	136	-0.0698	0.6428	0	0	0	0	0	0	1	-360	360;
	134	139	-0.0353	0.166	0	0	0	0	0	0	1	-360	360;
	134	141	-0.023	0.1179	0	0	0	0	0	0	1	-360	360;
	134	142	-0.0263	0.1167	0	0	0	0	0	0	1	-360	360;
	134	144	-0.0145	0.0435	0	0	0	0	0	0	1	-360	360;
	134	145	-0.0034	0.0216	0	0	0	0	0	0	1	-360	360;
	135	95	-0.3448	3.4845	0	0	0	0	0	0	1	-360	360;
	135	136	-0.0031	0.0178	0	0	0	0	0	0	1	-360	360;
	135	138	-0.0084	0.1729	0	0	0	0	0	0	1	-360	360;
	135	141	-0.129	0.6993	0	0	0	0	0	0	1	-360	360;
	136	115	-0.012	0.0855	0	0	0	0	0	0	1	-360	360;
	136	116	-1.2	4.2655	0	0	0	0	0	0	1	-360	360;
	136	117	-2.969	9.0875	0	0	0	0	0	0	1	-360	360;
	136	118	-0.5749	1.6206	0	0	0	0	0	0	1	-360	360;
	136	138	-0.1581	0.5485	0	0	0	0	0	0	1	-360	360;
	136	139	-0.0059	0.0293	0	0	0	0	0	0	1	-360	360;
	136	140	-2.403	9.378	0	0	0	0	0	0	1	-360	360;
	136	141	-0.0026	0.0175	0	0	0	0	0	0	1	-360	360;
	136	142	-0.0467	0.1709	0	0	0	0	0	0	1	-360	360;
	136	143	-1.762	3.4549	0	0	0	0	0	0	1	-360	360;
	136	145	-0.0049	0.0539	0	0	0	0	0	0	1	-360	360;
	137	139	-0.0183	0.0936	0	0	0	0	0	0	1	-360	360;
	137	140	-2.229	8.0228	0	0	0	0	0	0	1	-360	360;
	137	145	-0.0852	0.4071	0	0	0	0	0	0	1	-360	360;
	139	140	-0.0054	0.0239	0	0	0	0	0	0	1	-360	360;
	139	141	-0.0083	0.046	0	0	0	0	0	0	1	-360	360;
	139	142	-0.3102	1.267	0	0	0	0	0	0	1	-360	360;
	139	145	-0.0009	0.008	0	0	0	0	0	0	1	-360	360;
	140	145	-0.1088	0.48	0	0	0	0	0	0	1	-360	360;
	141	115	-0.0007	0.0131	0	0	0	0	0	0	1	-360	360;
	141	116	-0.1568	0.7448	0	0	0	0	0	0	1	-360	360;
	141	117	-0.3702	1.382	0	0	0	0	0	0	1	-360	360;
	141	118	-0.0414	0.1439	0	0	0	0	0	0	1	-360	360;
	141	131	-0.2331	0.8129	0	0	0	0	0	0	1	-360	360;
	141	132	-1.628	7.0936	0	0	0	0	0	0	1	-360	360;
	141	142	-0.0018	0.0105	0	0	0	0	0	0	1	-360	360;
	141	143	-0.0702	0.1778	0	0	0	0	0	0	1	-360	360;
	141	144	-0.0756	0.2441	0	0	0	0	0	0	1	-360	360;
	141	145	-0.0038	0.0358	0	0	0	0	0	0	1	-360	360;
	142	115	-0.0166	0.1563	0	0	0	0	0	0	1	-360	360;
	142	116	-0.6916	2.6302	0	0	0	0	0	0	1	-360	360;
	142	117	-0.5596	2.2284	0	0	0	0	0	0	1	-360	360;
	142	118	-0.0185	0.1037	0	0	0	0	0	0	1	-360	360;
	142	119	-0.2742	1.8611	0	0	0	0	0	0	1	-360	360;
	142	120	-0.6043	7.353	0	0	0	0	0	0	1	-360	360;
	142	122	-0.2589	2.1732	0	0	0	0	0	0	1	-360	360;
	142	124	-0.1736	2.1347	0	0	0	0	0	0	1	-360	360;
	142	125	-1.09	8.616	0	0	0	0	0	0	1	-360	360;
	142	130	-0.3608	1.8618	0	0	0	0	0	0	1	-360	360;
	142	131	-0.0013	0.0157	0	0	0	0	0	0	1	-360	360;
	142	132	-0.0055	0.081	0	0	0	0	0	0	1	-360	360;
	142	133	-1.636	9.1725	0	0	0	0	0	0	1	-360	360;
	142	143	-0.0038	0.0187	0	0	0	0	0	0	1	-360	360;
	142	144	-0.002	0.0229	0	0	0	0	0	0	1	-360	360;
	142	145	-0.0738	0.438	0	0	0	0	0	0	1	-360	360;
	143	144	-0.4863	2.3282	0	0	0	0	0	0	1	-360	360;
	144	145	-0.3835	1.2052	0	0	0	0	0	0	1	-360	360;
];

%%-----  OPF Data  -----%%
%% generator cost data
%	1	startup	shutdown	n	x1	y1	...	xn	yn
%	2	startup	shutdown	n	c(n-1)	...	c0
mpc.gencost = [
	2	0	0	3	0.196078431	20	0;
	2	0	0	3	0.0067294751	20	0;
	2	0	0	3	0.0399680256	20	0;
	2	0	0	3	0.212765957	20	0;
	2	0	0	3	0.142857143	20	0;
	2	0	0	3	0.014858841	20	0;
	2	0	0	3	0.454545455	20	0;
	2	0	0	3	0.15625	20	0;
	2	0	0	3	0.0142857143	20	0;
	2	0	0	3	0.0333333333	20	0;
	2	0	0	3	0.0763358779	20	0;
	2	0	0	3	0.166666667	20	0;
	2	0	0	3	0.0714285714	20	0;
	2	0	0	3	0.0234741784	20	0;
	2	0	0	3	0.05	20	0;
	2	0	0	3	0.0588235294	20	0;
	2	0	0	3	0.0321646832	20	0;
	2	0	0	3	0.00490196078	20	0;
	2	0	0	3	0.0740740741	20	0;
	2	0	0	3	0.005	20	0;
	2	0	0	3	0.00617283951	20	0;
	2	0	0	3	0.00925925926	20	0;
	2	0	0	3	0.0125	20	0;
	2	0	0	3	0.192307692	20	0;
	2	0	0	3	0.0142857143	20	0;
	2	0	0	3	0.005	20	0;
	2	0	0	3	0.0333333333	20	0;
	2	0	0	3	0.00401123145	20	0;
	2	0	0	3	0.00368595651	20	0;
	2	0	0	3	0.00380662352	20	0;
	2	0	0	3	0.00236966825	20	0;
	2	0	0	3	0.0011168193	20	0;
	2	0	0	3	0.00333667	20	0;
	2	0	0	3	0.00991080278	20	0;
	2	0	0	3	0.00332778702	20	0;
	2	0	0	3	0.000771426367	20	0;
	2	0	0	3	0.00168435237	20	0;
	2	0	0	3	0.00035335689	20	0;
	2	0	0	3	0.00323101777	20	0;
	2	0	0	3	0.000484824978	20	0;
	2	0	0	3	0.00167168171	20	0;
	2	0	0	3	0.000192492782	20	0;
	2	0	0	3	0.00082863772	20	0;
	2	0	0	3	0.000175951015	20	0;
	2	0	0	3	0.000432469835	20	0;
	2	0	0	3	0.000263775685	20	0;
	2	0	0	3	0.000409014684	20	0;
	2	0	0	3	0.00190331176	20	0;
	2	0	0	3	0.000877423883	20	0;
	2	0	0	3	0.000708284521	20	0;
];

%% bus names
mpc.bus_name = {
	'BRU   1A 500';
	'BRU   5A 500';
	'BRU  12A25.7';
	'BRU  13B25.7';
	'BRU  14C25.7';
	'MIL  15A 500';
	'NAN  17A 500';
	'NAN  19A 100';
	'TRA  20A 500';
	'TRA  21B 500';
	'TRA  22A 100';
	'CLA  23A 500';
	'CLA  25A 100';
	'ESS  26A 500';
	'ESS  29A 100';
	'ESS  30B 100';
	'HAN  31A 500';
	'HAN  33A 100';
	'HAN  34B 100';
	'HAN  35C 100';
	'HAN  36D 100';
	'POR  37A 500';
	'POR  38A 100';
	'PIN  39A 500';
	'CHE  41A 500';
	'CHE  42A 100';
	'LEN  43A 500';
	'LEN  46A 100';
	'LEN  47B 100';
	'POR  552 100';
	'CHE  56B 100';
	'TRA  57A 100';
	'BRU 101A 220';
	'BRU 110A 220';
	'BRU 111B 220';
	'DOU 112A 220';
	'OWE 113A 220';
	'OWE 114B 220';
	'WIN 117A 220';
	'WIN 118B 220';
	'SEA 119A 100';
	'SEA 120B 100';
	'SEA 121A 220';
	'SEA 122B 220';
	'STR 125A 220';
	'STR 126B 220';
	'HAN 127A 100';
	'HAN 128B 100';
	'HAN 129A 220';
	'HAN 130B 220';
	'ORA 131A 220';
	'FER 134A 100';
	'FER 135B 100';
	'SCH 138A 100';
	'SCH 139B 100';
	'ALL 142A 100';
	'ALL 143B 100';
	'ESS 144A 100';
	'HAN 163A 100';
	'LAK 187A 100';
	'DET 196A 220';
	'DET 198A 100';
	'BUC 205A 100';
	'GAL 259A 100';
	'GAL 260B 100';
	'NAN 269A 100';
	'BEC 287A 100';
	'BUR 317A 100';
	'TRA 318A 100';
	'MAN 344A 100';
	'MAN 347A 100';
	'CLA 364A 100';
	'CHE 424A 100';
	'CHE 425B 100';
	'LEN 520A 100';
	'PIN 539A 100';
	'DUM 5441 100';
	'POR 545A 100';
	'MCK1032  100';
	'HIG1036S 100';
	'SCA11548 100';
	'CHF1262  100';
	'POR1430T 100';
	'WIN1573A 100';
	'STR1574A 100';
	'DET15765 100';
	'OWE16165 100';
	'OWN16284 100';
	'CAN1751T 100';
	'AGU1752T 100';
	'ARN1754T 100';
	'AUB17551 100';
	'BRU1771318.5';
	'CAM17761 100';
	'CAR1777T 100';
	'CHE17801 100';
	'DEC1782T 100';
	'DES17831 100';
	'DOU1793T  18';
	'HOL17961 100';
	'LAK18068 100';
	'LAM18071 100';
	'LON1815T 100';
	'NAN18205 100';
	'PIC18252 100';
	'PIC18263 100';
	'RED1830T 100';
	'SAU18311 100';
	'STW18435 100';
	'BRU1853118.5';
	'NAN18563 100';
	'LAK18637 100';
	'BRU18705  24';
	'BRU18736  24';
	'18C2007E 100';
	'18H2016O 100';
	'19B2051R 100';
	'19M20792 100';
	'AK 2152  100';
	'ALB21531 100';
	'CHA2184G 100';
	'DUN22033 100';
	'GOU2246G 100';
	'HNT2264G 100';
	'MIL2296G 100';
	'SHO23701 100';
	'BEA24522 100';
	'BR.24593 100';
	'NOR25181 100';
	'BER26011 100';
	'CLV2609F 100';
	'ERI2616. 100';
	'WAR2645  100';
	'ALL2651  100';
	'BD 26524 100';
	'CAL26541 100';
	'CAP2655  100';
	'PIN26637 100';
	'SPP2666N 100';
	'SPP2669H 100';
	'VER2674N 100';
	'01A2679R 100';
	'03D2699E 100';
	'3BR2719  100';
	'8BO2739  100';
};

%   WARNINGS:
%       check the title format in the first line of the cdf file.
%       MVA limit of branch 1 - 2 not given, set to 0
%       MVA limit of branch 1 - 3 not given, set to 0
%       MVA limit of branch 1 - 4 not given, set to 0
%       MVA limit of branch 1 - 5 not given, set to 0
%       MVA limit of branch 1 - 93 not given, set to 0
%       MVA limit of branch 1 - 93 not given, set to 0
%       MVA limit of branch 2 - 113 not given, set to 0
%       MVA limit of branch 2 - 114 not given, set to 0
%       MVA limit of branch 3 - 33 not given, set to 0
%       MVA limit of branch 4 - 33 not given, set to 0
%       MVA limit of branch 5 - 33 not given, set to 0
%       MVA limit of branch 7 - 8 not given, set to 0
%       MVA limit of branch 7 - 104 not given, set to 0
%       MVA limit of branch 7 - 104 not given, set to 0
%       MVA limit of branch 8 - 66 not given, set to 0
%       MVA limit of branch 8 - 66 not given, set to 0
%       MVA limit of branch 9 - 11 not given, set to 0
%       MVA limit of branch 10 - 32 not given, set to 0
%       MVA limit of branch 11 - 69 not given, set to 0
%       MVA limit of branch 12 - 13 not given, set to 0
%       MVA limit of branch 12 - 13 not given, set to 0
%       MVA limit of branch 12 - 13 not given, set to 0
%       MVA limit of branch 12 - 72 not given, set to 0
%       MVA limit of branch 13 - 72 not given, set to 0
%       MVA limit of branch 13 - 72 not given, set to 0
%       MVA limit of branch 13 - 72 not given, set to 0
%       MVA limit of branch 14 - 15 not given, set to 0
%       MVA limit of branch 14 - 16 not given, set to 0
%       MVA limit of branch 15 - 58 not given, set to 0
%       MVA limit of branch 16 - 58 not given, set to 0
%       MVA limit of branch 17 - 18 not given, set to 0
%       MVA limit of branch 17 - 19 not given, set to 0
%       MVA limit of branch 17 - 20 not given, set to 0
%       MVA limit of branch 17 - 21 not given, set to 0
%       MVA limit of branch 18 - 59 not given, set to 0
%       MVA limit of branch 19 - 59 not given, set to 0
%       MVA limit of branch 20 - 59 not given, set to 0
%       MVA limit of branch 21 - 59 not given, set to 0
%       MVA limit of branch 22 - 23 not given, set to 0
%       MVA limit of branch 22 - 30 not given, set to 0
%       MVA limit of branch 22 - 78 not given, set to 0
%       MVA limit of branch 22 - 83 not given, set to 0
%       MVA limit of branch 23 - 83 not given, set to 0
%       MVA limit of branch 23 - 83 not given, set to 0
%       MVA limit of branch 24 - 76 not given, set to 0
%       MVA limit of branch 24 - 77 not given, set to 0
%       MVA limit of branch 25 - 26 not given, set to 0
%       MVA limit of branch 25 - 31 not given, set to 0
%       MVA limit of branch 26 - 73 not given, set to 0
%       MVA limit of branch 27 - 28 not given, set to 0
%       MVA limit of branch 27 - 29 not given, set to 0
%       MVA limit of branch 28 - 75 not given, set to 0
%       MVA limit of branch 29 - 75 not given, set to 0
%       MVA limit of branch 30 - 78 not given, set to 0
%       MVA limit of branch 31 - 74 not given, set to 0
%       MVA limit of branch 32 - 69 not given, set to 0
%       MVA limit of branch 33 - 37 not given, set to 0
%       MVA limit of branch 33 - 110 not given, set to 0
%       MVA limit of branch 33 - 110 not given, set to 0
%       MVA limit of branch 36 - 99 not given, set to 0
%       MVA limit of branch 37 - 87 not given, set to 0
%       MVA limit of branch 37 - 88 not given, set to 0
%       MVA limit of branch 38 - 88 not given, set to 0
%       MVA limit of branch 39 - 84 not given, set to 0
%       MVA limit of branch 40 - 84 not given, set to 0
%       MVA limit of branch 41 - 42 not given, set to 0
%       MVA limit of branch 41 - 43 not given, set to 0
%       MVA limit of branch 42 - 44 not given, set to 0
%       MVA limit of branch 45 - 85 not given, set to 0
%       MVA limit of branch 46 - 85 not given, set to 0
%       MVA limit of branch 47 - 48 not given, set to 0
%       MVA limit of branch 47 - 50 not given, set to 0
%       MVA limit of branch 47 - 87 not given, set to 0
%       MVA limit of branch 48 - 49 not given, set to 0
%       MVA limit of branch 48 - 87 not given, set to 0
%       MVA limit of branch 52 - 53 not given, set to 0
%       MVA limit of branch 52 - 54 not given, set to 0
%       MVA limit of branch 53 - 55 not given, set to 0
%       MVA limit of branch 54 - 55 not given, set to 0
%       MVA limit of branch 56 - 57 not given, set to 0
%       MVA limit of branch 56 - 58 not given, set to 0
%       MVA limit of branch 57 - 58 not given, set to 0
%       MVA limit of branch 58 - 59 not given, set to 0
%       MVA limit of branch 58 - 72 not given, set to 0
%       MVA limit of branch 58 - 87 not given, set to 0
%       MVA limit of branch 58 - 98 not given, set to 0
%       MVA limit of branch 58 - 100 not given, set to 0
%       MVA limit of branch 58 - 103 not given, set to 0
%       MVA limit of branch 59 - 60 not given, set to 0
%       MVA limit of branch 59 - 72 not given, set to 0
%       MVA limit of branch 59 - 79 not given, set to 0
%       MVA limit of branch 59 - 80 not given, set to 0
%       MVA limit of branch 59 - 89 not given, set to 0
%       MVA limit of branch 59 - 92 not given, set to 0
%       MVA limit of branch 59 - 94 not given, set to 0
%       MVA limit of branch 59 - 98 not given, set to 0
%       MVA limit of branch 59 - 100 not given, set to 0
%       MVA limit of branch 59 - 103 not given, set to 0
%       MVA limit of branch 59 - 107 not given, set to 0
%       MVA limit of branch 60 - 135 not given, set to 0
%       MVA limit of branch 60 - 79 not given, set to 0
%       MVA limit of branch 60 - 80 not given, set to 0
%       MVA limit of branch 60 - 90 not given, set to 0
%       MVA limit of branch 60 - 92 not given, set to 0
%       MVA limit of branch 60 - 94 not given, set to 0
%       MVA limit of branch 60 - 95 not given, set to 0
%       MVA limit of branch 60 - 138 not given, set to 0
%       MVA limit of branch 61 - 62 not given, set to 0
%       MVA limit of branch 61 - 62 not given, set to 0
%       MVA limit of branch 62 - 86 not given, set to 0
%       MVA limit of branch 62 - 86 not given, set to 0
%       MVA limit of branch 63 - 64 not given, set to 0
%       MVA limit of branch 63 - 65 not given, set to 0
%       MVA limit of branch 63 - 66 not given, set to 0
%       MVA limit of branch 63 - 67 not given, set to 0
%       MVA limit of branch 63 - 69 not given, set to 0
%       MVA limit of branch 63 - 102 not given, set to 0
%       MVA limit of branch 63 - 102 not given, set to 0
%       MVA limit of branch 63 - 102 not given, set to 0
%       MVA limit of branch 63 - 102 not given, set to 0
%       MVA limit of branch 63 - 116 not given, set to 0
%       MVA limit of branch 63 - 117 not given, set to 0
%       MVA limit of branch 63 - 118 not given, set to 0
%       MVA limit of branch 63 - 124 not given, set to 0
%       MVA limit of branch 64 - 65 not given, set to 0
%       MVA limit of branch 64 - 66 not given, set to 0
%       MVA limit of branch 64 - 67 not given, set to 0
%       MVA limit of branch 64 - 69 not given, set to 0
%       MVA limit of branch 64 - 97 not given, set to 0
%       MVA limit of branch 64 - 124 not given, set to 0
%       MVA limit of branch 65 - 66 not given, set to 0
%       MVA limit of branch 65 - 67 not given, set to 0
%       MVA limit of branch 65 - 69 not given, set to 0
%       MVA limit of branch 65 - 97 not given, set to 0
%       MVA limit of branch 65 - 124 not given, set to 0
%       MVA limit of branch 66 - 67 not given, set to 0
%       MVA limit of branch 66 - 68 not given, set to 0
%       MVA limit of branch 66 - 69 not given, set to 0
%       MVA limit of branch 66 - 97 not given, set to 0
%       MVA limit of branch 66 - 111 not given, set to 0
%       MVA limit of branch 66 - 111 not given, set to 0
%       MVA limit of branch 66 - 111 not given, set to 0
%       MVA limit of branch 66 - 111 not given, set to 0
%       MVA limit of branch 66 - 124 not given, set to 0
%       MVA limit of branch 67 - 68 not given, set to 0
%       MVA limit of branch 67 - 69 not given, set to 0
%       MVA limit of branch 67 - 97 not given, set to 0
%       MVA limit of branch 67 - 119 not given, set to 0
%       MVA limit of branch 67 - 120 not given, set to 0
%       MVA limit of branch 67 - 121 not given, set to 0
%       MVA limit of branch 67 - 122 not given, set to 0
%       MVA limit of branch 67 - 124 not given, set to 0
%       MVA limit of branch 67 - 125 not given, set to 0
%       MVA limit of branch 67 - 132 not given, set to 0
%       MVA limit of branch 68 - 69 not given, set to 0
%       MVA limit of branch 69 - 70 not given, set to 0
%       MVA limit of branch 69 - 71 not given, set to 0
%       MVA limit of branch 69 - 72 not given, set to 0
%       MVA limit of branch 69 - 73 not given, set to 0
%       MVA limit of branch 69 - 74 not given, set to 0
%       MVA limit of branch 69 - 97 not given, set to 0
%       MVA limit of branch 69 - 101 not given, set to 0
%       MVA limit of branch 69 - 112 not given, set to 0
%       MVA limit of branch 69 - 124 not given, set to 0
%       MVA limit of branch 70 - 71 not given, set to 0
%       MVA limit of branch 70 - 72 not given, set to 0
%       MVA limit of branch 70 - 73 not given, set to 0
%       MVA limit of branch 70 - 74 not given, set to 0
%       MVA limit of branch 70 - 101 not given, set to 0
%       MVA limit of branch 70 - 112 not given, set to 0
%       MVA limit of branch 71 - 72 not given, set to 0
%       MVA limit of branch 71 - 73 not given, set to 0
%       MVA limit of branch 71 - 74 not given, set to 0
%       MVA limit of branch 71 - 101 not given, set to 0
%       MVA limit of branch 71 - 112 not given, set to 0
%       MVA limit of branch 72 - 73 not given, set to 0
%       MVA limit of branch 72 - 74 not given, set to 0
%       MVA limit of branch 72 - 98 not given, set to 0
%       MVA limit of branch 72 - 100 not given, set to 0
%       MVA limit of branch 72 - 101 not given, set to 0
%       MVA limit of branch 72 - 103 not given, set to 0
%       MVA limit of branch 72 - 112 not given, set to 0
%       MVA limit of branch 73 - 74 not given, set to 0
%       MVA limit of branch 73 - 75 not given, set to 0
%       MVA limit of branch 73 - 81 not given, set to 0
%       MVA limit of branch 73 - 82 not given, set to 0
%       MVA limit of branch 73 - 91 not given, set to 0
%       MVA limit of branch 73 - 96 not given, set to 0
%       MVA limit of branch 73 - 101 not given, set to 0
%       MVA limit of branch 73 - 105 not given, set to 0
%       MVA limit of branch 73 - 105 not given, set to 0
%       MVA limit of branch 73 - 105 not given, set to 0
%       MVA limit of branch 73 - 108 not given, set to 0
%       MVA limit of branch 73 - 109 not given, set to 0
%       MVA limit of branch 73 - 112 not given, set to 0
%       MVA limit of branch 73 - 121 not given, set to 0
%       MVA limit of branch 74 - 75 not given, set to 0
%       MVA limit of branch 74 - 81 not given, set to 0
%       MVA limit of branch 74 - 82 not given, set to 0
%       MVA limit of branch 74 - 91 not given, set to 0
%       MVA limit of branch 74 - 96 not given, set to 0
%       MVA limit of branch 74 - 101 not given, set to 0
%       MVA limit of branch 74 - 106 not given, set to 0
%       MVA limit of branch 74 - 106 not given, set to 0
%       MVA limit of branch 74 - 108 not given, set to 0
%       MVA limit of branch 74 - 109 not given, set to 0
%       MVA limit of branch 74 - 112 not given, set to 0
%       MVA limit of branch 74 - 121 not given, set to 0
%       MVA limit of branch 75 - 82 not given, set to 0
%       MVA limit of branch 75 - 91 not given, set to 0
%       MVA limit of branch 75 - 96 not given, set to 0
%       MVA limit of branch 75 - 108 not given, set to 0
%       MVA limit of branch 75 - 109 not given, set to 0
%       MVA limit of branch 75 - 121 not given, set to 0
%       MVA limit of branch 76 - 77 not given, set to 0
%       MVA limit of branch 76 - 89 not given, set to 0
%       MVA limit of branch 79 - 80 not given, set to 0
%       MVA limit of branch 79 - 90 not given, set to 0
%       MVA limit of branch 79 - 92 not given, set to 0
%       MVA limit of branch 79 - 94 not given, set to 0
%       MVA limit of branch 79 - 95 not given, set to 0
%       MVA limit of branch 79 - 107 not given, set to 0
%       MVA limit of branch 80 - 90 not given, set to 0
%       MVA limit of branch 80 - 92 not given, set to 0
%       MVA limit of branch 80 - 94 not given, set to 0
%       MVA limit of branch 82 - 91 not given, set to 0
%       MVA limit of branch 82 - 108 not given, set to 0
%       MVA limit of branch 82 - 109 not given, set to 0
%       MVA limit of branch 82 - 121 not given, set to 0
%       MVA limit of branch 83 - 89 not given, set to 0
%       MVA limit of branch 89 - 103 not given, set to 0
%       MVA limit of branch 90 - 92 not given, set to 0
%       MVA limit of branch 90 - 94 not given, set to 0
%       MVA limit of branch 91 - 96 not given, set to 0
%       MVA limit of branch 91 - 108 not given, set to 0
%       MVA limit of branch 91 - 109 not given, set to 0
%       MVA limit of branch 91 - 121 not given, set to 0
%       MVA limit of branch 92 - 94 not given, set to 0
%       MVA limit of branch 92 - 107 not given, set to 0
%       MVA limit of branch 94 - 95 not given, set to 0
%       MVA limit of branch 94 - 138 not given, set to 0
%       MVA limit of branch 95 - 138 not given, set to 0
%       MVA limit of branch 96 - 108 not given, set to 0
%       MVA limit of branch 97 - 124 not given, set to 0
%       MVA limit of branch 98 - 100 not given, set to 0
%       MVA limit of branch 98 - 103 not given, set to 0
%       MVA limit of branch 100 - 103 not given, set to 0
%       MVA limit of branch 101 - 112 not given, set to 0
%       MVA limit of branch 102 - 117 not given, set to 0
%       MVA limit of branch 102 - 118 not given, set to 0
%       MVA limit of branch 108 - 109 not given, set to 0
%       MVA limit of branch 108 - 121 not given, set to 0
%       MVA limit of branch 109 - 121 not given, set to 0
%       MVA limit of branch 115 - 116 not given, set to 0
%       MVA limit of branch 115 - 117 not given, set to 0
%       MVA limit of branch 115 - 118 not given, set to 0
%       MVA limit of branch 115 - 143 not given, set to 0
%       MVA limit of branch 116 - 117 not given, set to 0
%       MVA limit of branch 116 - 118 not given, set to 0
%       MVA limit of branch 116 - 143 not given, set to 0
%       MVA limit of branch 117 - 118 not given, set to 0
%       MVA limit of branch 117 - 143 not given, set to 0
%       MVA limit of branch 118 - 131 not given, set to 0
%       MVA limit of branch 118 - 132 not given, set to 0
%       MVA limit of branch 118 - 143 not given, set to 0
%       MVA limit of branch 119 - 120 not given, set to 0
%       MVA limit of branch 119 - 121 not given, set to 0
%       MVA limit of branch 119 - 122 not given, set to 0
%       MVA limit of branch 119 - 124 not given, set to 0
%       MVA limit of branch 119 - 125 not given, set to 0
%       MVA limit of branch 119 - 126 not given, set to 0
%       MVA limit of branch 119 - 127 not given, set to 0
%       MVA limit of branch 119 - 128 not given, set to 0
%       MVA limit of branch 119 - 129 not given, set to 0
%       MVA limit of branch 119 - 130 not given, set to 0
%       MVA limit of branch 119 - 131 not given, set to 0
%       MVA limit of branch 119 - 132 not given, set to 0
%       MVA limit of branch 119 - 144 not given, set to 0
%       MVA limit of branch 120 - 121 not given, set to 0
%       MVA limit of branch 120 - 122 not given, set to 0
%       MVA limit of branch 120 - 123 not given, set to 0
%       MVA limit of branch 120 - 124 not given, set to 0
%       MVA limit of branch 120 - 125 not given, set to 0
%       MVA limit of branch 120 - 127 not given, set to 0
%       MVA limit of branch 120 - 128 not given, set to 0
%       MVA limit of branch 120 - 129 not given, set to 0
%       MVA limit of branch 120 - 130 not given, set to 0
%       MVA limit of branch 120 - 131 not given, set to 0
%       MVA limit of branch 120 - 132 not given, set to 0
%       MVA limit of branch 121 - 122 not given, set to 0
%       MVA limit of branch 121 - 123 not given, set to 0
%       MVA limit of branch 121 - 124 not given, set to 0
%       MVA limit of branch 121 - 125 not given, set to 0
%       MVA limit of branch 121 - 127 not given, set to 0
%       MVA limit of branch 121 - 128 not given, set to 0
%       MVA limit of branch 121 - 129 not given, set to 0
%       MVA limit of branch 121 - 131 not given, set to 0
%       MVA limit of branch 121 - 132 not given, set to 0
%       MVA limit of branch 122 - 123 not given, set to 0
%       MVA limit of branch 122 - 124 not given, set to 0
%       MVA limit of branch 122 - 125 not given, set to 0
%       MVA limit of branch 122 - 131 not given, set to 0
%       MVA limit of branch 122 - 132 not given, set to 0
%       MVA limit of branch 122 - 133 not given, set to 0
%       MVA limit of branch 122 - 143 not given, set to 0
%       MVA limit of branch 123 - 124 not given, set to 0
%       MVA limit of branch 123 - 125 not given, set to 0
%       MVA limit of branch 123 - 131 not given, set to 0
%       MVA limit of branch 123 - 132 not given, set to 0
%       MVA limit of branch 124 - 125 not given, set to 0
%       MVA limit of branch 124 - 128 not given, set to 0
%       MVA limit of branch 124 - 131 not given, set to 0
%       MVA limit of branch 124 - 132 not given, set to 0
%       MVA limit of branch 124 - 133 not given, set to 0
%       MVA limit of branch 124 - 143 not given, set to 0
%       MVA limit of branch 125 - 127 not given, set to 0
%       MVA limit of branch 125 - 128 not given, set to 0
%       MVA limit of branch 125 - 129 not given, set to 0
%       MVA limit of branch 125 - 130 not given, set to 0
%       MVA limit of branch 125 - 131 not given, set to 0
%       MVA limit of branch 125 - 132 not given, set to 0
%       MVA limit of branch 127 - 128 not given, set to 0
%       MVA limit of branch 127 - 129 not given, set to 0
%       MVA limit of branch 128 - 129 not given, set to 0
%       MVA limit of branch 128 - 130 not given, set to 0
%       MVA limit of branch 128 - 131 not given, set to 0
%       MVA limit of branch 130 - 131 not given, set to 0
%       MVA limit of branch 130 - 132 not given, set to 0
%       MVA limit of branch 130 - 144 not given, set to 0
%       MVA limit of branch 131 - 132 not given, set to 0
%       MVA limit of branch 131 - 133 not given, set to 0
%       MVA limit of branch 131 - 143 not given, set to 0
%       MVA limit of branch 131 - 144 not given, set to 0
%       MVA limit of branch 132 - 133 not given, set to 0
%       MVA limit of branch 132 - 143 not given, set to 0
%       MVA limit of branch 132 - 144 not given, set to 0
%       MVA limit of branch 133 - 143 not given, set to 0
%       MVA limit of branch 134 - 131 not given, set to 0
%       MVA limit of branch 134 - 136 not given, set to 0
%       MVA limit of branch 134 - 139 not given, set to 0
%       MVA limit of branch 134 - 141 not given, set to 0
%       MVA limit of branch 134 - 142 not given, set to 0
%       MVA limit of branch 134 - 144 not given, set to 0
%       MVA limit of branch 134 - 145 not given, set to 0
%       MVA limit of branch 135 - 95 not given, set to 0
%       MVA limit of branch 135 - 136 not given, set to 0
%       MVA limit of branch 135 - 138 not given, set to 0
%       MVA limit of branch 135 - 141 not given, set to 0
%       MVA limit of branch 136 - 115 not given, set to 0
%       MVA limit of branch 136 - 116 not given, set to 0
%       MVA limit of branch 136 - 117 not given, set to 0
%       MVA limit of branch 136 - 118 not given, set to 0
%       MVA limit of branch 136 - 138 not given, set to 0
%       MVA limit of branch 136 - 139 not given, set to 0
%       MVA limit of branch 136 - 140 not given, set to 0
%       MVA limit of branch 136 - 141 not given, set to 0
%       MVA limit of branch 136 - 142 not given, set to 0
%       MVA limit of branch 136 - 143 not given, set to 0
%       MVA limit of branch 136 - 145 not given, set to 0
%       MVA limit of branch 137 - 139 not given, set to 0
%       MVA limit of branch 137 - 140 not given, set to 0
%       MVA limit of branch 137 - 145 not given, set to 0
%       MVA limit of branch 139 - 140 not given, set to 0
%       MVA limit of branch 139 - 141 not given, set to 0
%       MVA limit of branch 139 - 142 not given, set to 0
%       MVA limit of branch 139 - 145 not given, set to 0
%       MVA limit of branch 140 - 145 not given, set to 0
%       MVA limit of branch 141 - 115 not given, set to 0
%       MVA limit of branch 141 - 116 not given, set to 0
%       MVA limit of branch 141 - 117 not given, set to 0
%       MVA limit of branch 141 - 118 not given, set to 0
%       MVA limit of branch 141 - 131 not given, set to 0
%       MVA limit of branch 141 - 132 not given, set to 0
%       MVA limit of branch 141 - 142 not given, set to 0
%       MVA limit of branch 141 - 143 not given, set to 0
%       MVA limit of branch 141 - 144 not given, set to 0
%       MVA limit of branch 141 - 145 not given, set to 0
%       MVA limit of branch 142 - 115 not given, set to 0
%       MVA limit of branch 142 - 116 not given, set to 0
%       MVA limit of branch 142 - 117 not given, set to 0
%       MVA limit of branch 142 - 118 not given, set to 0
%       MVA limit of branch 142 - 119 not given, set to 0
%       MVA limit of branch 142 - 120 not given, set to 0
%       MVA limit of branch 142 - 122 not given, set to 0
%       MVA limit of branch 142 - 124 not given, set to 0
%       MVA limit of branch 142 - 125 not given, set to 0
%       MVA limit of branch 142 - 130 not given, set to 0
%       MVA limit of branch 142 - 131 not given, set to 0
%       MVA limit of branch 142 - 132 not given, set to 0
%       MVA limit of branch 142 - 133 not given, set to 0
%       MVA limit of branch 142 - 143 not given, set to 0
%       MVA limit of branch 142 - 144 not given, set to 0
%       MVA limit of branch 142 - 145 not given, set to 0
%       MVA limit of branch 143 - 144 not given, set to 0
%       MVA limit of branch 144 - 145 not given, set to 0
