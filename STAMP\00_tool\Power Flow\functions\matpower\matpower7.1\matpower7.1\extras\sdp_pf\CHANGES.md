Change history for [SDP-PF][1]
==============================


Version 1.0.2 - *Oct 8, 2020*
-----------------------------

#### 10/8/20
  - Released 1.0.1. (with MATPOWER 7.0)

#### 9/23/20
  - Use `have_feature()` everywhere in place of deprecated `have_fcn()`.


Version 1.0.1 - *Jun 20, 2019*
------------------------------

#### 1/23/19
  - Released 1.0.1. (with MATPOWER 7.0)
  - Add Octave compatibility.
  - Add Travis-CI integration.

#### 1/11/19
  - Add `test_sdp_pf()` function.

#### 5/3/17
  - Add checks for "no suitable solver" error.

#### 9/22/16
  - Turn off some MATLAB warnings.

#### 3/20/15 (with MATPOWER 5.1)
  - Switch to more permissive 3-clause BSD license from GPL 3.0.


Version 1.0 - *Jan 17, 2014*
----------------------------

#### 1/17/14
  - Released 1.0. (with MATPOWER 5.0b1)

---

[1]: https://github.com/MATPOWER/mx-sdp_pf
