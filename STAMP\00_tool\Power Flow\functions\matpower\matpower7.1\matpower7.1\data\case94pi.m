function mpc = case94pi
%CASE94PI  Power flow data for 70 bus distribution system from Pires, et al
%   Please see CASEFORMAT for details on the case file format.
%
%   Data from ...
%       Pires DF, Antunes CH, Martins AG (2012) NSGA-II with local search for
%       a multi-objective reactive power compensation problem. Int J Electr
%       Power Energy Syst 43:313-324. doi: 10.1016/j.ijepes.2012.05.024
%       URL: https://doi.org/10.1016/j.ijepes.2012.05.024

%% MATPOWER Case Format : Version 2
mpc.version = '2';

%%-----  Power Flow Data  -----%%
%% system MVA base
mpc.baseMVA = 1;

%% bus data
%	bus_i	type	Pd	Qd	Gs	Bs	area	Vm	Va	baseKV	zone	Vmax	Vmin
mpc.bus = [ %% (Pd and Qd are specified in kW & kVAr here, converted to MW & MVAr below)
	1	3	0	0	0	0	1	1	0	15	1	1	1;
	2	1	22.5	10.9	0	0	1	1	0	15	1	1.1	0.9;
	3	1	240.3	116.4	0	0	1	1	0	15	1	1.1	0.9;
	4	1	24.3	11.8	0	0	1	1	0	15	1	1.1	0.9;
	5	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	6	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	7	1	28.8	14	0	0	1	1	0	15	1	1.1	0.9;
	8	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	9	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	10	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	11	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	12	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	13	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	14	1	57.6	27.9	0	0	1	1	0	15	1	1.1	0.9;
	15	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	16	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	17	1	18.9	9.2	0	0	1	1	0	15	1	1.1	0.9;
	18	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	19	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	20	1	55.8	27	0	0	1	1	0	15	1	1.1	0.9;
	21	1	40.5	19.6	0	0	1	1	0	15	1	1.1	0.9;
	22	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	23	1	54	26.2	0	0	1	1	0	15	1	1.1	0.9;
	24	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	25	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	26	1	46.8	22.7	0	0	1	1	0	15	1	1.1	0.9;
	27	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	28	1	0	0	0	0	1	1	0	15	1	1.1	0.9;
	29	1	13.5	6.5	0	0	1	1	0	15	1	1.1	0.9;
	30	1	3.6	1.7	0	0	1	1	0	15	1	1.1	0.9;
	31	1	18	8.7	0	0	1	1	0	15	1	1.1	0.9;
	32	1	21.6	10.5	0	0	1	1	0	15	1	1.1	0.9;
	33	1	9	4.4	0	0	1	1	0	15	1	1.1	0.9;
	34	1	64.8	31.4	0	0	1	1	0	15	1	1.1	0.9;
	35	1	65.7	31.8	0	0	1	1	0	15	1	1.1	0.9;
	36	1	59.4	28.8	0	0	1	1	0	15	1	1.1	0.9;
	37	1	13.5	6.5	0	0	1	1	0	15	1	1.1	0.9;
	38	1	161.1	78	0	0	1	1	0	15	1	1.1	0.9;
	39	1	26.1	12.6	0	0	1	1	0	15	1	1.1	0.9;
	40	1	134.1	65	0	0	1	1	0	15	1	1.1	0.9;
	41	1	85.5	41.4	0	0	1	1	0	15	1	1.1	0.9;
	42	1	41.4	20.1	0	0	1	1	0	15	1	1.1	0.9;
	43	1	41.4	20.1	0	0	1	1	0	15	1	1.1	0.9;
	44	1	41.4	20.1	0	0	1	1	0	15	1	1.1	0.9;
	45	1	21.6	10.5	0	0	1	1	0	15	1	1.1	0.9;
	46	1	25.2	12.2	0	0	1	1	0	15	1	1.1	0.9;
	47	1	45.9	22.2	0	0	1	1	0	15	1	1.1	0.9;
	48	1	36.9	17.9	0	0	1	1	0	15	1	1.1	0.9;
	49	1	63.9	31	0	0	1	1	0	15	1	1.1	0.9;
	50	1	68.4	33.1	0	0	1	1	0	15	1	1.1	0.9;
	51	1	27.9	13.5	0	0	1	1	0	15	1	1.1	0.9;
	52	1	81	39.2	0	0	1	1	0	15	1	1.1	0.9;
	53	1	69.3	33.6	0	0	1	1	0	15	1	1.1	0.9;
	54	1	62.1	30.1	0	0	1	1	0	15	1	1.1	0.9;
	55	1	35.1	17	0	0	1	1	0	15	1	1.1	0.9;
	56	1	205.2	99.4	0	0	1	1	0	15	1	1.1	0.9;
	57	1	31.5	15.3	0	0	1	1	0	15	1	1.1	0.9;
	58	1	521.1	252.4	0	0	1	1	0	15	1	1.1	0.9;
	59	1	212.4	102.9	0	0	1	1	0	15	1	1.1	0.9;
	60	1	39.6	19.2	0	0	1	1	0	15	1	1.1	0.9;
	61	1	45	21.8	0	0	1	1	0	15	1	1.1	0.9;
	62	1	17.1	8.3	0	0	1	1	0	15	1	1.1	0.9;
	63	1	21.6	10.5	0	0	1	1	0	15	1	1.1	0.9;
	64	1	35.1	17	0	0	1	1	0	15	1	1.1	0.9;
	65	1	70.2	34	0	0	1	1	0	15	1	1.1	0.9;
	66	1	34.2	16.6	0	0	1	1	0	15	1	1.1	0.9;
	67	1	22.5	10.9	0	0	1	1	0	15	1	1.1	0.9;
	68	1	45.9	22.2	0	0	1	1	0	15	1	1.1	0.9;
	69	1	33.3	16.1	0	0	1	1	0	15	1	1.1	0.9;
	70	1	36.9	17.9	0	0	1	1	0	15	1	1.1	0.9;
	71	1	45	21.8	0	0	1	1	0	15	1	1.1	0.9;
	72	1	75.6	36.6	0	0	1	1	0	15	1	1.1	0.9;
	73	1	67.5	32.7	0	0	1	1	0	15	1	1.1	0.9;
	74	1	27.9	13.5	0	0	1	1	0	15	1	1.1	0.9;
	75	1	38.7	18.7	0	0	1	1	0	15	1	1.1	0.9;
	76	1	53.1	25.7	0	0	1	1	0	15	1	1.1	0.9;
	77	1	65.7	31.8	0	0	1	1	0	15	1	1.1	0.9;
	78	1	63	30.5	0	0	1	1	0	15	1	1.1	0.9;
	79	1	67.5	32.7	0	0	1	1	0	15	1	1.1	0.9;
	80	1	45	21.8	0	0	1	1	0	15	1	1.1	0.9;
	81	1	9	4.4	0	0	1	1	0	15	1	1.1	0.9;
	82	1	16.2	7.8	0	0	1	1	0	15	1	1.1	0.9;
	83	1	67.5	32.7	0	0	1	1	0	15	1	1.1	0.9;
	84	1	296.1	143.4	0	0	1	1	0	15	1	1.1	0.9;
	85	1	72	34.9	0	0	1	1	0	15	1	1.1	0.9;
	86	1	76.5	37.1	0	0	1	1	0	15	1	1.1	0.9;
	87	1	90.9	44	0	0	1	1	0	15	1	1.1	0.9;
	88	1	72	34.9	0	0	1	1	0	15	1	1.1	0.9;
	89	1	63	30.5	0	0	1	1	0	15	1	1.1	0.9;
	90	1	21.6	10.5	0	0	1	1	0	15	1	1.1	0.9;
	91	1	36.9	17.9	0	0	1	1	0	15	1	1.1	0.9;
	92	1	20.7	10	0	0	1	1	0	15	1	1.1	0.9;
	93	1	17.1	8.3	0	0	1	1	0	15	1	1.1	0.9;
	94	1	90	43.6	0	0	1	1	0	15	1	1.1	0.9;
];

%% generator data
%	bus	Pg	Qg	Qmax	Qmin	Vg	mBase	status	Pmax	Pmin	Pc1	Pc2	Qc1min	Qc1max	Qc2min	Qc2max	ramp_agc	ramp_10	ramp_30	ramp_q	apf
mpc.gen = [
	1	0	0	10	-10	1	100	1	10	0	0	0	0	0	0	0	0	0	0	0	0;
];

%% branch data
%	fbus	tbus	r	x	b	rateA	rateB	rateC	ratio	angle	status	angmin	angmax
mpc.branch = [  %% (r and x specified in ohms here, converted to p.u. below)
	1	2	0.112	0.1873	0	0	0	0	0	0	1	-360	360;
	2	3	0.0763	0.1274	0	0	0	0	0	0	1	-360	360;
	3	4	0.1891	0.3161	0	0	0	0	0	0	1	-360	360;
	4	5	0.2243	0.3749	0	0	0	0	0	0	1	-360	360;
	5	6	0.2571	0.4297	0	0	0	0	0	0	1	-360	360;
	6	7	0.134	0.2239	0	0	0	0	0	0	1	-360	360;
	7	8	0.2986	0.4991	0	0	0	0	0	0	1	-360	360;
	8	9	0.1953	0.3265	0	0	0	0	0	0	1	-360	360;
	9	10	0.5097	0.8519	0	0	0	0	0	0	1	-360	360;
	10	11	1.5303	1.5101	0	0	0	0	0	0	1	-360	360;
	11	12	0.1889	0.1864	0	0	0	0	0	0	1	-360	360;
	12	13	0.1816	0.1793	0	0	0	0	0	0	1	-360	360;
	13	14	0.0661	0.0653	0	0	0	0	0	0	1	-360	360;
	14	15	0.4115	0.4061	0	0	0	0	0	0	1	-360	360;
	15	16	0.2584	0.255	0	0	0	0	0	0	1	-360	360;
	16	17	0.2033	0.2006	0	0	0	0	0	0	1	-360	360;
	17	18	0.7243	0.7148	0	0	0	0	0	0	1	-360	360;
	18	19	0.2162	0.2134	0	0	0	0	0	0	1	-360	360;
	19	20	0.35	0.3454	0	0	0	0	0	0	1	-360	360;
	20	21	1.4775	0.3891	0	0	0	0	0	0	1	-360	360;
	21	22	0.45	0.1185	0	0	0	0	0	0	1	-360	360;
	22	23	0.771	0.203	0	0	0	0	0	0	1	-360	360;
	23	24	0.885	0.2331	0	0	0	0	0	0	1	-360	360;
	24	25	0.9915	0.2611	0	0	0	0	0	0	1	-360	360;
	25	26	0.384	0.1011	0	0	0	0	0	0	1	-360	360;
	26	27	0.7245	0.1908	0	0	0	0	0	0	1	-360	360;
	27	28	1.185	0.3121	0	0	0	0	0	0	1	-360	360;
	28	29	1.2353	0.6899	0	0	0	0	0	0	1	-360	360;
	29	30	0.3557	0.1987	0	0	0	0	0	0	1	-360	360;
	30	31	0.9494	0.3406	0	0	0	0	0	0	1	-360	360;
	31	32	0.6899	0.3853	0	0	0	0	0	0	1	-360	360;
	32	33	1.5707	0.8773	0	0	0	0	0	0	1	-360	360;
	5	34	1.2655	0.454	0	0	0	0	0	0	1	-360	360;
	5	35	0.1688	0.0943	0	0	0	0	0	0	1	-360	360;
	35	36	0.2741	0.1531	0	0	0	0	0	0	1	-360	360;
	36	37	0.2552	0.1425	0	0	0	0	0	0	1	-360	360;
	6	38	0.4165	0.2326	0	0	0	0	0	0	1	-360	360;
	6	39	1.4835	0.3907	0	0	0	0	0	0	1	-360	360;
	39	40	1.8	0.474	0	0	0	0	0	0	1	-360	360;
	40	41	0.5177	0.2892	0	0	0	0	0	0	1	-360	360;
	41	42	0.7148	0.3992	0	0	0	0	0	0	1	-360	360;
	8	43	1.0575	0.2785	0	0	0	0	0	0	1	-360	360;
	43	44	0.5198	0.2903	0	0	0	0	0	0	1	-360	360;
	44	45	0.3341	0.1866	0	0	0	0	0	0	1	-360	360;
	9	46	0.349	0.1949	0	0	0	0	0	0	1	-360	360;
	10	47	0.5771	0.3223	0	0	0	0	0	0	1	-360	360;
	47	48	0.3598	0.2009	0	0	0	0	0	0	1	-360	360;
	48	49	0.7688	0.4294	0	0	0	0	0	0	1	-360	360;
	49	50	0.2599	0.1451	0	0	0	0	0	0	1	-360	360;
	50	51	0.8654	0.4833	0	0	0	0	0	0	1	-360	360;
	10	52	0.5248	0.5179	0	0	0	0	0	0	1	-360	360;
	52	53	0.1737	0.1714	0	0	0	0	0	0	1	-360	360;
	53	54	0.6148	0.6068	0	0	0	0	0	0	1	-360	360;
	54	55	0.198	0.1954	0	0	0	0	0	0	1	-360	360;
	55	56	0.198	0.1954	0	0	0	0	0	0	1	-360	360;
	56	57	0.285	0.2813	0	0	0	0	0	0	1	-360	360;
	57	58	0.1429	0.141	0	0	0	0	0	0	1	-360	360;
	58	59	0.3409	0.1904	0	0	0	0	0	0	1	-360	360;
	59	60	0.3679	0.2055	0	0	0	0	0	0	1	-360	360;
	60	61	0.3591	0.2006	0	0	0	0	0	0	1	-360	360;
	61	62	0.3503	0.1957	0	0	0	0	0	0	1	-360	360;
	62	63	0.4219	0.2356	0	0	0	0	0	0	1	-360	360;
	63	64	1.538	0.5517	0	0	0	0	0	0	1	-360	360;
	64	65	0.9788	0.3511	0	0	0	0	0	0	1	-360	360;
	65	66	1.4911	0.5349	0	0	0	0	0	0	1	-360	360;
	11	67	0.969	0.2552	0	0	0	0	0	0	1	-360	360;
	67	68	0.6705	0.1766	0	0	0	0	0	0	1	-360	360;
	12	69	0.4354	0.2432	0	0	0	0	0	0	1	-360	360;
	13	70	0.4631	0.2586	0	0	0	0	0	0	1	-360	360;
	70	71	0.2707	0.1512	0	0	0	0	0	0	1	-360	360;
	15	72	0.6683	0.3732	0	0	0	0	0	0	1	-360	360;
	72	73	0.8525	0.4762	0	0	0	0	0	0	1	-360	360;
	16	74	0.3314	0.1851	0	0	0	0	0	0	1	-360	360;
	18	75	0.405	0.2262	0	0	0	0	0	0	1	-360	360;
	19	76	0.4367	0.2439	0	0	0	0	0	0	1	-360	360;
	19	77	0.3416	0.1908	0	0	0	0	0	0	1	-360	360;
	77	78	0.2113	0.118	0	0	0	0	0	0	1	-360	360;
	78	79	1.1249	0.4035	0	0	0	0	0	0	1	-360	360;
	79	80	1.1738	0.6556	0	0	0	0	0	0	1	-360	360;
	80	81	0.619	0.3457	0	0	0	0	0	0	1	-360	360;
	81	82	0.5684	0.3174	0	0	0	0	0	0	1	-360	360;
	20	83	0.8393	0.3011	0	0	0	0	0	0	1	-360	360;
	83	84	0.2133	0.1191	0	0	0	0	0	0	1	-360	360;
	84	85	0.3645	0.2036	0	0	0	0	0	0	1	-360	360;
	85	86	0.3206	0.1791	0	0	0	0	0	0	1	-360	360;
	22	87	0.7675	0.4286	0	0	0	0	0	0	1	-360	360;
	24	88	1.5914	0.5709	0	0	0	0	0	0	1	-360	360;
	25	89	0.702	0.3921	0	0	0	0	0	0	1	-360	360;
	25	90	20.743	0.7441	0	0	0	0	0	0	1	-360	360;
	90	91	0.678	0.2432	0	0	0	0	0	0	1	-360	360;
	91	92	0.5738	0.3205	0	0	0	0	0	0	1	-360	360;
	27	93	0.5913	0.3303	0	0	0	0	0	0	1	-360	360;
	28	94	1.1865	0.3124	0	0	0	0	0	0	1	-360	360;
];

%%-----  OPF Data  -----%%
%% generator cost data
%	1	startup	shutdown	n	x1	y1	...	xn	yn
%	2	startup	shutdown	n	c(n-1)	...	c0
mpc.gencost = [
	2	0	0	3	0	20	0;
	2	0	0	3	0	20	0;
];


%% convert branch impedances from Ohms to p.u.
[PQ, PV, REF, NONE, BUS_I, BUS_TYPE, PD, QD, GS, BS, BUS_AREA, VM, ...
    VA, BASE_KV, ZONE, VMAX, VMIN, LAM_P, LAM_Q, MU_VMAX, MU_VMIN] = idx_bus;
[F_BUS, T_BUS, BR_R, BR_X, BR_B, RATE_A, RATE_B, RATE_C, ...
    TAP, SHIFT, BR_STATUS, PF, QF, PT, QT, MU_SF, MU_ST, ...
    ANGMIN, ANGMAX, MU_ANGMIN, MU_ANGMAX] = idx_brch;
Vbase = mpc.bus(1, BASE_KV) * 1e3;      %% in Volts
Sbase = mpc.baseMVA * 1e6;              %% in VA
mpc.branch(:, [BR_R BR_X]) = mpc.branch(:, [BR_R BR_X]) / (Vbase^2 / Sbase);

%% convert loads from kW to MW
mpc.bus(:, [PD, QD]) = mpc.bus(:, [PD, QD]) / 1e3;
