\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyperFirstAtBeginDocument{\AtBeginDocument}
\HyperFirstAtBeginDocument{\ifx\hyper@anchor\@undefined
\global\let\oldcontentsline\contentsline
\gdef\contentsline#1#2#3#4{\oldcontentsline{#1}{#2}{#3}}
\global\let\oldnewlabel\newlabel
\gdef\newlabel#1#2{\newlabelxx{#1}#2}
\gdef\newlabelxx#1#2#3#4#5#6{\oldnewlabel{#1}{{#2}{#3}}}
\AtEndDocument{\ifx\hyper@anchor\@undefined
\let\contentsline\oldcontentsline
\let\newlabel\oldnewlabel
\fi}
\fi}
\global\let\hyper@last\relax 
\gdef\HyperFirstAtBeginDocument#1{#1}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\citation{octave}
\citation{zimmerman2011,matpower}
\@writefile{toc}{\contentsline {section}{\numberline {1}Introduction}{7}{section.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.1}Background}{7}{subsection.1.1}\protected@file@percent }
\@writefile{brf}{\backcite{zimmerman2011,matpower}{{7}{1.1}{subsection.1.1}}}
\@writefile{brf}{\backcite{octave}{{7}{1}{subsection.1.1}}}
\citation{bsd}
\@writefile{toc}{\contentsline {subsection}{\numberline {1.2}License and Terms of Use}{8}{subsection.1.2}\protected@file@percent }
\@writefile{brf}{\backcite{bsd}{{8}{1.2}{subsection.1.2}}}
\citation{mpom_manual}
\@writefile{toc}{\contentsline {subsection}{\numberline {1.3}Citing \unhbox \voidb@x \hbox {MP-Opt-Model}{}}{9}{subsection.1.3}\protected@file@percent }
\@writefile{brf}{\backcite{mpom_manual}{{9}{1.3}{subsection.1.3}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {1.4}\unhbox \voidb@x \hbox {MP-Opt-Model}{} Development}{9}{subsection.1.4}\protected@file@percent }
\newlabel{sec:development}{{1.4}{9}{\mpom {} Development}{subsection.1.4}{}}
\citation{octave}
\citation{wang2007a,mips_manual}
\@writefile{toc}{\contentsline {section}{\numberline {2}Getting Started}{10}{section.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1}System Requirements}{10}{subsection.2.1}\protected@file@percent }
\newlabel{sec:sysreq}{{2.1}{10}{System Requirements}{subsection.2.1}{}}
\@writefile{brf}{\backcite{wang2007a, mips_manual}{{10}{2.1}{subsection.2.1}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2}Installation}{10}{subsection.2.2}\protected@file@percent }
\newlabel{sec:installation}{{2.2}{10}{Installation}{subsection.2.2}{}}
\@writefile{brf}{\backcite{octave}{{10}{4}{subsection.2.1}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.3}Sample Usage}{11}{subsection.2.3}\protected@file@percent }
\newlabel{sec:usage}{{2.3}{11}{Sample Usage}{subsection.2.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.4}Documentation}{14}{subsection.2.4}\protected@file@percent }
\newlabel{sec:documentation}{{2.4}{14}{Documentation}{subsection.2.4}{}}
\@writefile{toc}{\contentsline {section}{\numberline {3}\unhbox \voidb@x \hbox {MP-Opt-Model}{} -- Overview}{15}{section.3}\protected@file@percent }
\newlabel{sec:mpom}{{3}{15}{\mpom {} -- Overview}{section.3}{}}
\@writefile{toc}{\contentsline {section}{\numberline {4}Solver Interface Functions}{16}{section.4}\protected@file@percent }
\newlabel{sec:master_solvers}{{4}{16}{Solver Interface Functions}{section.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1}LP/QP Solvers -- {\tt  qps\_master}}{16}{subsection.4.1}\protected@file@percent }
\newlabel{sec:qps_master}{{4.1}{16}{LP/QP Solvers -- {\tt qps\_master}}{subsection.4.1}{}}
\newlabel{eq:LPobj}{{4.1}{16}{LP/QP Solvers -- {\tt qps\_master}}{equation.4.1}{}}
\newlabel{eq:LPvarbounds}{{4.3}{16}{LP/QP Solvers -- {\tt qps\_master}}{equation.4.2}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}1}{\ignorespaces Input Arguments for {\relsize  {-0.5}{\tt  {{qps\_master}}}}\TPToverlap {\textsuperscript  {\dag  }}\relax }}{17}{table.caption.4}\protected@file@percent }
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{tab:qps_master_input}{{4\unhbox \voidb@x \hbox {-}1}{17}{Input Arguments for \code {qps\_master}\tnote {\dag }\relax }{table.caption.4}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}2}{\ignorespaces Output Arguments for {\relsize  {-0.5}{\tt  {{qps\_master}}}}\TPToverlap {\textsuperscript  {\dag  }}\relax }}{17}{table.caption.6}\protected@file@percent }
\newlabel{tab:qps_master_output}{{4\unhbox \voidb@x \hbox {-}2}{17}{Output Arguments for \code {qps\_master}\tnote {\dag }\relax }{table.caption.6}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}3}{\ignorespaces Options for {\relsize  {-0.5}{\tt  {{qps\_master}}}}\relax }}{18}{table.caption.8}\protected@file@percent }
\newlabel{tab:qps_master_options}{{4\unhbox \voidb@x \hbox {-}3}{18}{Options for \code {qps\_master}\relax }{table.caption.8}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.1.1}QP Example}{19}{subsubsection.4.1.1}\protected@file@percent }
\newlabel{sec:qp_ex}{{4.1.1}{19}{QP Example}{subsubsection.4.1.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2}MILP/MIQP Solvers -- {\tt  miqps\_master}}{20}{subsection.4.2}\protected@file@percent }
\newlabel{sec:miqps_master}{{4.2}{20}{MILP/MIQP Solvers -- {\tt miqps\_master}}{subsection.4.2}{}}
\newlabel{eq:mi_integer}{{4.4}{20}{MILP/MIQP Solvers -- {\tt miqps\_master}}{equation.4.4}{}}
\newlabel{eq:mi_binary}{{4.5}{20}{MILP/MIQP Solvers -- {\tt miqps\_master}}{equation.4.5}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}4}{\ignorespaces Input Arguments for {\relsize  {-0.5}{\tt  {{miqps\_master}}}}\relax }}{20}{table.caption.10}\protected@file@percent }
\newlabel{tab:miqps_master_input}{{4\unhbox \voidb@x \hbox {-}4}{20}{Input Arguments for \code {miqps\_master}\relax }{table.caption.10}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}5}{\ignorespaces Options for {\relsize  {-0.5}{\tt  {{miqps\_master}}}}\relax }}{21}{table.caption.12}\protected@file@percent }
\newlabel{tab:miqps_master_options}{{4\unhbox \voidb@x \hbox {-}5}{21}{Options for \code {miqps\_master}\relax }{table.caption.12}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.2.1}MILP Example}{22}{subsubsection.4.2.1}\protected@file@percent }
\newlabel{sec:milp_ex}{{4.2.1}{22}{MILP Example}{subsubsection.4.2.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3}NLP Solvers -- {\tt  nlps\_master}}{22}{subsection.4.3}\protected@file@percent }
\newlabel{sec:nlps_master}{{4.3}{22}{NLP Solvers -- {\tt nlps\_master}}{subsection.4.3}{}}
\newlabel{eq:nlp_obj}{{4.6}{22}{NLP Solvers -- {\tt nlps\_master}}{equation.4.6}{}}
\newlabel{eq:nlp_g}{{4.7}{22}{NLP Solvers -- {\tt nlps\_master}}{equation.4.7}{}}
\newlabel{eq:nlp_h}{{4.8}{22}{NLP Solvers -- {\tt nlps\_master}}{equation.4.7}{}}
\newlabel{eq:nlp_linear_constraints}{{4.9}{22}{NLP Solvers -- {\tt nlps\_master}}{equation.4.7}{}}
\newlabel{eq:nlp_var_bounds}{{4.10}{22}{NLP Solvers -- {\tt nlps\_master}}{equation.4.7}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}6}{\ignorespaces Input Arguments for {\relsize  {-0.5}{\tt  {{nlps\_master}}}}\TPToverlap {\textsuperscript  {\dag  }}\relax }}{23}{table.caption.14}\protected@file@percent }
\newlabel{tab:nlps_master_input}{{4\unhbox \voidb@x \hbox {-}6}{23}{Input Arguments for \code {nlps\_master}\tnote {\dag }\relax }{table.caption.14}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}7}{\ignorespaces Output Arguments for {\relsize  {-0.5}{\tt  {{nlps\_master}}}}\relax }}{24}{table.caption.16}\protected@file@percent }
\newlabel{tab:nlps_master_output}{{4\unhbox \voidb@x \hbox {-}7}{24}{Output Arguments for \code {nlps\_master}\relax }{table.caption.16}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}8}{\ignorespaces Options for {\relsize  {-0.5}{\tt  {{nlps\_master}}}}\relax }}{24}{table.caption.18}\protected@file@percent }
\newlabel{tab:nlps_master_options}{{4\unhbox \voidb@x \hbox {-}8}{24}{Options for \code {nlps\_master}\relax }{table.caption.18}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.3.1}NLP Example 1}{25}{subsubsection.4.3.1}\protected@file@percent }
\newlabel{sec:nlp_ex1}{{4.3.1}{25}{NLP Example 1}{subsubsection.4.3.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.3.2}NLP Example 2}{26}{subsubsection.4.3.2}\protected@file@percent }
\newlabel{sec:nlp_ex2}{{4.3.2}{26}{NLP Example 2}{subsubsection.4.3.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.4}Nonlinear Equation Solvers -- {\tt  nleqs\_master}}{29}{subsection.4.4}\protected@file@percent }
\newlabel{sec:nleqs_master}{{4.4}{29}{Nonlinear Equation Solvers -- {\tt nleqs\_master}}{subsection.4.4}{}}
\newlabel{eq:nleq}{{4.16}{29}{Nonlinear Equation Solvers -- {\tt nleqs\_master}}{equation.4.16}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}9}{\ignorespaces Input Arguments for {\relsize  {-0.5}{\tt  {{nleqs\_master}}}}\relax }}{30}{table.caption.20}\protected@file@percent }
\newlabel{tab:nleqs_master_input}{{4\unhbox \voidb@x \hbox {-}9}{30}{Input Arguments for \code {nleqs\_master}\relax }{table.caption.20}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}10}{\ignorespaces Output Arguments for {\relsize  {-0.5}{\tt  {{nleqs\_master}}}}\TPToverlap {\textsuperscript  {\dag  }}\relax }}{31}{table.caption.22}\protected@file@percent }
\newlabel{tab:nleqs_master_output}{{4\unhbox \voidb@x \hbox {-}10}{31}{Output Arguments for \code {nleqs\_master}\tnote {\dag }\relax }{table.caption.22}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.4.1}NLEQ Example 1}{31}{subsubsection.4.4.1}\protected@file@percent }
\newlabel{sec:nleq_ex1}{{4.4.1}{31}{NLEQ Example 1}{subsubsection.4.4.1}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}11}{\ignorespaces Options for {\relsize  {-0.5}{\tt  {{nleqs\_master}}}}\relax }}{32}{table.caption.24}\protected@file@percent }
\newlabel{tab:nleqs_master_options}{{4\unhbox \voidb@x \hbox {-}11}{32}{Options for \code {nleqs\_master}\relax }{table.caption.24}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.4.2}NLEQ Example 2}{34}{subsubsection.4.4.2}\protected@file@percent }
\newlabel{sec:nleq_ex2}{{4.4.2}{34}{NLEQ Example 2}{subsubsection.4.4.2}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5}Optimization Model Class -- {\tt  opt\_model}}{38}{section.5}\protected@file@percent }
\newlabel{sec:opt_model}{{5}{38}{Optimization Model Class -- {\tt opt\_model}}{section.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.1}Adding Variables}{38}{subsection.5.1}\protected@file@percent }
\newlabel{sec:add_var}{{5.1}{38}{Adding Variables}{subsection.5.1}{}}
\newlabel{eq:x}{{5.1}{39}{Adding Variables}{equation.5.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.1.1}Variable Subsets}{39}{subsubsection.5.1.1}\protected@file@percent }
\newlabel{sec:varsets}{{5.1.1}{39}{Variable Subsets}{subsubsection.5.1.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.2}Adding Constraints}{40}{subsection.5.2}\protected@file@percent }
\newlabel{sec:constraint}{{5.2}{40}{Adding Constraints}{subsection.5.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.2.1}Linear Constraints}{40}{subsubsection.5.2.1}\protected@file@percent }
\newlabel{sec:add_lin_constraint}{{5.2.1}{40}{Linear Constraints}{subsubsection.5.2.1}{}}
\newlabel{eq:linear_constraints}{{5.2}{40}{Linear Constraints}{equation.5.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.2.2}General Nonlinear Constraints}{41}{subsubsection.5.2.2}\protected@file@percent }
\newlabel{sec:add_nln_constraint}{{5.2.2}{41}{General Nonlinear Constraints}{subsubsection.5.2.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.3}Adding Costs}{42}{subsection.5.3}\protected@file@percent }
\newlabel{sec:add_cost}{{5.3}{42}{Adding Costs}{subsection.5.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.3.1}Quadratic Costs}{43}{subsubsection.5.3.1}\protected@file@percent }
\newlabel{sec:add_quad_cost}{{5.3.1}{43}{Quadratic Costs}{subsubsection.5.3.1}{}}
\newlabel{eq:quad_cost}{{5.11}{43}{Quadratic Costs}{equation.5.11}{}}
\newlabel{eq:quad_cost2}{{5.12}{43}{Quadratic Costs}{equation.5.12}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.3.2}General Nonlinear Costs}{44}{subsubsection.5.3.2}\protected@file@percent }
\newlabel{sec:add_nln_cost}{{5.3.2}{44}{General Nonlinear Costs}{subsubsection.5.3.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.4}Solving the Model}{45}{subsection.5.4}\protected@file@percent }
\newlabel{sec:solve}{{5.4}{45}{Solving the Model}{subsection.5.4}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}1}{\ignorespaces Options for {\relsize  {-0.5}{\tt  {{solve}}}}\relax }}{46}{table.caption.26}\protected@file@percent }
\newlabel{tab:solve_options}{{5\unhbox \voidb@x \hbox {-}1}{46}{Options for \code {solve}\relax }{table.caption.26}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}2}{\ignorespaces Values for {\relsize  {-0.5}{\tt  {{alg}}}} Option to {\relsize  {-0.5}{\tt  {{solve}}}}\relax }}{47}{table.caption.28}\protected@file@percent }
\newlabel{tab:solve_alg_option}{{5\unhbox \voidb@x \hbox {-}2}{47}{Values for \code {alg} Option to \code {solve}\relax }{table.caption.28}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.5}Accessing the Model}{47}{subsection.5.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.5.1}Indexing}{47}{subsubsection.5.5.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}3}{\ignorespaces Example Indexing Data\relax }}{48}{table.caption.30}\protected@file@percent }
\newlabel{tab:vv}{{5\unhbox \voidb@x \hbox {-}3}{48}{Example Indexing Data\relax }{table.caption.30}{}}
\newlabel{sec:get_idx}{{5.5.1}{48}{\code {get\_idx}}{section*.31}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}4}{\ignorespaces Example Indexing Data\relax }}{49}{table.caption.33}\protected@file@percent }
\newlabel{tab:set_types}{{5\unhbox \voidb@x \hbox {-}4}{49}{Example Indexing Data\relax }{table.caption.33}{}}
\newlabel{sec:describe_idx}{{5.5.1}{49}{\code {describe\_idx}}{section*.35}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.5.2}Variables}{50}{subsubsection.5.5.2}\protected@file@percent }
\newlabel{sec:params_var}{{5.5.2}{50}{\code {params\_var}}{section*.36}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.5.3}Constraints}{51}{subsubsection.5.5.3}\protected@file@percent }
\newlabel{sec:eval_nln_constraint}{{5.5.3}{52}{\code {eval\_nln\_constraint}}{section*.39}{}}
\newlabel{sec:eval_nln_constraint_hess}{{5.5.3}{52}{\code {eval\_nln\_constraint\_hess}}{section*.40}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.5.4}Costs}{53}{subsubsection.5.5.4}\protected@file@percent }
\newlabel{sec:eval_quad_cost}{{5.5.4}{54}{\code {eval\_quad\_cost}}{section*.43}{}}
\newlabel{sec:eval_nln_cost}{{5.5.4}{54}{\code {eval\_nln\_cost}}{section*.44}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.5.5}Model Solution}{55}{subsubsection.5.5.5}\protected@file@percent }
\newlabel{sec:soln}{{5.5.5}{55}{Model Solution}{subsubsection.5.5.5}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}5}{\ignorespaces Model Solution\relax }}{55}{table.caption.46}\protected@file@percent }
\newlabel{tab:soln}{{5\unhbox \voidb@x \hbox {-}5}{55}{Model Solution\relax }{table.caption.46}{}}
\newlabel{sec:get_soln}{{5.5.5}{55}{\code {get\_soln}}{section*.47}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}6}{\ignorespaces Inputs for {\relsize  {-0.5}{\tt  {{get\_soln}}}}\relax }}{56}{table.caption.50}\protected@file@percent }
\newlabel{tab:get_soln}{{5\unhbox \voidb@x \hbox {-}6}{56}{Inputs for \code {get\_soln}\relax }{table.caption.50}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}7}{\ignorespaces Values of {\relsize  {-0.5}{\tt  {{tags}}}} input to {\relsize  {-0.5}{\tt  {{get\_soln}}}}\relax }}{57}{table.caption.52}\protected@file@percent }
\newlabel{tab:get_soln_tags}{{5\unhbox \voidb@x \hbox {-}7}{57}{Values of \code {tags} input to \code {get\_soln}\relax }{table.caption.52}{}}
\newlabel{sec:parse_soln}{{5.5.5}{58}{\code {parse\_soln}}{section*.53}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}8}{\ignorespaces Output of {\relsize  {-0.5}{\tt  {{parse\_soln}}}}\relax }}{58}{table.caption.55}\protected@file@percent }
\newlabel{tab:parse_soln}{{5\unhbox \voidb@x \hbox {-}8}{58}{Output of \code {parse\_soln}\relax }{table.caption.55}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.6}Modifying the Model}{59}{subsection.5.6}\protected@file@percent }
\newlabel{sec:modifying}{{5.6}{59}{Modifying the Model}{subsection.5.6}{}}
\newlabel{sec:set_params}{{5.6}{59}{\code {set\_params}}{section*.56}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}9}{\ignorespaces Inputs for {\relsize  {-0.5}{\tt  {{set\_params}}}}\relax }}{59}{table.caption.59}\protected@file@percent }
\newlabel{tab:set_params}{{5\unhbox \voidb@x \hbox {-}9}{59}{Inputs for \code {set\_params}\relax }{table.caption.59}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.7}Indexed Sets}{60}{subsection.5.7}\protected@file@percent }
\newlabel{sec:indexed_sets}{{5.7}{60}{Indexed Sets}{subsection.5.7}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.8}Miscellaneous Methods}{61}{subsection.5.8}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.8.1}Public Methods}{61}{subsubsection.5.8.1}\protected@file@percent }
\newlabel{sec:display}{{5.8.1}{62}{\code {display}}{section*.64}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.8.2}Private Methods}{64}{subsubsection.5.8.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.9}{\sc  Matpower}{} Index Manager Base Class -- {\tt  mp\_idx\_manager}}{64}{subsection.5.9}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}10}{\ignorespaces {\sc  Matpower}{} Index Manager ({\relsize  {-0.5}{\tt  {{mp\_idx\_manager}}}}) Properties and Methods\relax }}{65}{table.caption.75}\protected@file@percent }
\newlabel{tab:mp_idx_manager2}{{5\unhbox \voidb@x \hbox {-}10}{65}{\matpower {} Index Manager (\code {mp\_idx\_manager}) Properties and Methods\relax }{table.caption.75}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}11}{\ignorespaces {\sc  Matpower}{} Index Manager ({\relsize  {-0.5}{\tt  {{mp\_idx\_manager}}}}) Object Structure\relax }}{66}{table.caption.77}\protected@file@percent }
\newlabel{tab:obj_structure}{{5\unhbox \voidb@x \hbox {-}11}{66}{\matpower {} Index Manager (\code {mp\_idx\_manager}) Object Structure\relax }{table.caption.77}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.10}Reference}{67}{subsection.5.10}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.10.1}Properties}{67}{subsubsection.5.10.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}12}{\ignorespaces {\relsize  {-0.5}{\tt  {{opt\_model}}}} Properties\relax }}{67}{table.caption.79}\protected@file@percent }
\newlabel{tab:properties}{{5\unhbox \voidb@x \hbox {-}12}{67}{\code {opt\_model} Properties\relax }{table.caption.79}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.10.2}Methods}{67}{subsubsection.5.10.2}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}13}{\ignorespaces {\relsize  {-0.5}{\tt  {{opt\_model}}}} Methods\relax }}{68}{table.caption.81}\protected@file@percent }
\newlabel{tab:methods}{{5\unhbox \voidb@x \hbox {-}13}{68}{\code {opt\_model} Methods\relax }{table.caption.81}{}}
\@writefile{toc}{\contentsline {section}{\numberline {6}Utility Functions}{69}{section.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.1}\tt  have\_fcn}{69}{subsection.6.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.2}\tt  mpomver}{69}{subsection.6.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.3}\tt  nested\_struct\_copy}{69}{subsection.6.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.4}Private Feature Detection Functions}{69}{subsection.6.4}\protected@file@percent }
\newlabel{sec:featuredetection}{{6.4}{69}{Private Feature Detection Functions}{subsection.6.4}{}}
\citation{opti}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.1}\tt  have\_feature\_bpmpd}{70}{subsubsection.6.4.1}\protected@file@percent }
\newlabel{sec:have_feature_bpmpd}{{6.4.1}{70}{\tt have\_feature\_bpmpd}{subsubsection.6.4.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.2}\tt  have\_feature\_catchme}{70}{subsubsection.6.4.2}\protected@file@percent }
\newlabel{sec:have_feature_catchme}{{6.4.2}{70}{\tt have\_feature\_catchme}{subsubsection.6.4.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.3}\tt  have\_feature\_clp}{70}{subsubsection.6.4.3}\protected@file@percent }
\newlabel{sec:have_feature_clp}{{6.4.3}{70}{\tt have\_feature\_clp}{subsubsection.6.4.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.4}\tt  have\_feature\_opti\_clp}{70}{subsubsection.6.4.4}\protected@file@percent }
\newlabel{sec:have_feature_opti_clp}{{6.4.4}{70}{\tt have\_feature\_opti\_clp}{subsubsection.6.4.4}{}}
\@writefile{brf}{\backcite{opti}{{70}{6.4.4}{subsubsection.6.4.4}}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.5}\tt  have\_feature\_cplex}{70}{subsubsection.6.4.5}\protected@file@percent }
\newlabel{sec:have_feature_cplex}{{6.4.5}{70}{\tt have\_feature\_cplex}{subsubsection.6.4.5}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.6}\tt  have\_feature\_evalc}{70}{subsubsection.6.4.6}\protected@file@percent }
\newlabel{sec:have_feature_evalc}{{6.4.6}{70}{\tt have\_feature\_evalc}{subsubsection.6.4.6}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.7}\tt  have\_feature\_fmincon}{70}{subsubsection.6.4.7}\protected@file@percent }
\newlabel{sec:have_feature_fmincon}{{6.4.7}{70}{\tt have\_feature\_fmincon}{subsubsection.6.4.7}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.8}\tt  have\_feature\_fmincon\_ipm}{71}{subsubsection.6.4.8}\protected@file@percent }
\newlabel{sec:have_feature_fmincon_ipm}{{6.4.8}{71}{\tt have\_feature\_fmincon\_ipm}{subsubsection.6.4.8}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.9}\tt  have\_feature\_fsolve}{71}{subsubsection.6.4.9}\protected@file@percent }
\newlabel{sec:have_feature_fsolve}{{6.4.9}{71}{\tt have\_feature\_fsolve}{subsubsection.6.4.9}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.10}\tt  have\_feature\_glpk}{71}{subsubsection.6.4.10}\protected@file@percent }
\newlabel{sec:have_feature_glpk}{{6.4.10}{71}{\tt have\_feature\_glpk}{subsubsection.6.4.10}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.11}\tt  have\_feature\_gurobi}{71}{subsubsection.6.4.11}\protected@file@percent }
\newlabel{sec:have_feature_gurobi}{{6.4.11}{71}{\tt have\_feature\_gurobi}{subsubsection.6.4.11}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.12}\tt  have\_feature\_intlinprog}{71}{subsubsection.6.4.12}\protected@file@percent }
\newlabel{sec:have_feature_intlinprog}{{6.4.12}{71}{\tt have\_feature\_intlinprog}{subsubsection.6.4.12}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.13}\tt  have\_feature\_ipopt}{71}{subsubsection.6.4.13}\protected@file@percent }
\newlabel{sec:have_feature_ipopt}{{6.4.13}{71}{\tt have\_feature\_ipopt}{subsubsection.6.4.13}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.14}\tt  have\_feature\_ipopt\_auxdata}{71}{subsubsection.6.4.14}\protected@file@percent }
\newlabel{sec:have_feature_ipopt_auxdata}{{6.4.14}{71}{\tt have\_feature\_ipopt\_auxdata}{subsubsection.6.4.14}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.15}\tt  have\_feature\_isequaln}{72}{subsubsection.6.4.15}\protected@file@percent }
\newlabel{sec:have_feature_isequaln}{{6.4.15}{72}{\tt have\_feature\_isequaln}{subsubsection.6.4.15}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.16}\tt  have\_feature\_knitro}{72}{subsubsection.6.4.16}\protected@file@percent }
\newlabel{sec:have_feature_knitro}{{6.4.16}{72}{\tt have\_feature\_knitro}{subsubsection.6.4.16}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.17}\tt  have\_feature\_knitromatlab}{72}{subsubsection.6.4.17}\protected@file@percent }
\newlabel{sec:have_feature_knitromatlab}{{6.4.17}{72}{\tt have\_feature\_knitromatlab}{subsubsection.6.4.17}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.18}\tt  have\_feature\_ktrlink}{72}{subsubsection.6.4.18}\protected@file@percent }
\newlabel{sec:have_feature_ktrlink}{{6.4.18}{72}{\tt have\_feature\_ktrlink}{subsubsection.6.4.18}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.19}\tt  have\_feature\_linprog}{72}{subsubsection.6.4.19}\protected@file@percent }
\newlabel{sec:have_feature_linprog}{{6.4.19}{72}{\tt have\_feature\_linprog}{subsubsection.6.4.19}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.20}\tt  have\_feature\_linprog\_ds}{72}{subsubsection.6.4.20}\protected@file@percent }
\newlabel{sec:have_feature_linprog_ds}{{6.4.20}{72}{\tt have\_feature\_linprog\_ds}{subsubsection.6.4.20}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.21}\tt  have\_feature\_mosek}{72}{subsubsection.6.4.21}\protected@file@percent }
\newlabel{sec:have_feature_mosek}{{6.4.21}{72}{\tt have\_feature\_mosek}{subsubsection.6.4.21}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.22}\tt  have\_feature\_optim}{73}{subsubsection.6.4.22}\protected@file@percent }
\newlabel{sec:have_feature_optim}{{6.4.22}{73}{\tt have\_feature\_optim}{subsubsection.6.4.22}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.23}\tt  have\_feature\_optimoptions}{73}{subsubsection.6.4.23}\protected@file@percent }
\newlabel{sec:have_feature_optimoptions}{{6.4.23}{73}{\tt have\_feature\_optimoptions}{subsubsection.6.4.23}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.24}\tt  have\_feature\_osqp}{73}{subsubsection.6.4.24}\protected@file@percent }
\newlabel{sec:have_feature_osqp}{{6.4.24}{73}{\tt have\_feature\_osqp}{subsubsection.6.4.24}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.25}\tt  have\_feature\_quadprog}{73}{subsubsection.6.4.25}\protected@file@percent }
\newlabel{sec:have_feature_quadprog}{{6.4.25}{73}{\tt have\_feature\_quadprog}{subsubsection.6.4.25}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.26}\tt  have\_feature\_quadprog\_ls}{73}{subsubsection.6.4.26}\protected@file@percent }
\newlabel{sec:have_feature_quadprog_ls}{{6.4.26}{73}{\tt have\_feature\_quadprog\_ls}{subsubsection.6.4.26}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.27}\tt  have\_feature\_sdpt3}{73}{subsubsection.6.4.27}\protected@file@percent }
\newlabel{sec:have_feature_sdpt3}{{6.4.27}{73}{\tt have\_feature\_sdpt3}{subsubsection.6.4.27}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.28}\tt  have\_feature\_sedumi}{73}{subsubsection.6.4.28}\protected@file@percent }
\newlabel{sec:have_feature_sedumi}{{6.4.28}{73}{\tt have\_feature\_sedumi}{subsubsection.6.4.28}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.4.29}\tt  have\_feature\_yalmip}{74}{subsubsection.6.4.29}\protected@file@percent }
\newlabel{sec:have_feature_yalmip}{{6.4.29}{74}{\tt have\_feature\_yalmip}{subsubsection.6.4.29}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.5}{\sc  Matpower}{}-related Functions}{74}{subsection.6.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.5.1}\tt  mpopt2nleqopt}{74}{subsubsection.6.5.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.5.2}\tt  mpopt2nlpopt}{74}{subsubsection.6.5.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {6.5.3}\tt  mpopt2qpopt}{74}{subsubsection.6.5.3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {7}Acknowledgments}{76}{section.7}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{Appendix \numberline {A}\unhbox \voidb@x \hbox {MP-Opt-Model}{} Files, Functions and Classes}{77}{Appendix.1.A}\protected@file@percent }
\newlabel{app:functions}{{A}{77}{\mpom {} Files, Functions and Classes}{Appendix.1.A}{}}
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}1}{\ignorespaces \unhbox \voidb@x \hbox {MP-Opt-Model}{} Files and Functions\relax }}{77}{table.caption.83}\protected@file@percent }
\newlabel{tab:files}{{A\unhbox \voidb@x \hbox {-}1}{77}{\mpom {} Files and Functions\relax }{table.caption.83}{}}
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}2}{\ignorespaces Solver Functions\relax }}{78}{table.caption.85}\protected@file@percent }
\newlabel{tab:solvers}{{A\unhbox \voidb@x \hbox {-}2}{78}{Solver Functions\relax }{table.caption.85}{}}
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}3}{\ignorespaces Solver Options, etc.\relax }}{79}{table.caption.87}\protected@file@percent }
\newlabel{tab:solver_options}{{A\unhbox \voidb@x \hbox {-}3}{79}{Solver Options, etc.\relax }{table.caption.87}{}}
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}4}{\ignorespaces Optimization Model Class\relax }}{80}{table.caption.89}\protected@file@percent }
\newlabel{tab:opt_model}{{A\unhbox \voidb@x \hbox {-}4}{80}{Optimization Model Class\relax }{table.caption.89}{}}
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}5}{\ignorespaces {\sc  Matpower}{} Index Manager Class\relax }}{81}{table.caption.91}\protected@file@percent }
\newlabel{tab:mp_idx_manager}{{A\unhbox \voidb@x \hbox {-}5}{81}{\matpower {} Index Manager Class\relax }{table.caption.91}{}}
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}6}{\ignorespaces Utility Functions\relax }}{81}{table.caption.93}\protected@file@percent }
\newlabel{tab:utility}{{A\unhbox \voidb@x \hbox {-}6}{81}{Utility Functions\relax }{table.caption.93}{}}
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}7}{\ignorespaces Feature Detection Functions\TPToverlap {\textsuperscript  {*}}\relax }}{82}{table.caption.95}\protected@file@percent }
\newlabel{tab:have_feature_fcns}{{A\unhbox \voidb@x \hbox {-}7}{82}{Feature Detection Functions\tnote {*}\relax }{table.caption.95}{}}
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}8}{\ignorespaces \unhbox \voidb@x \hbox {MP-Opt-Model}{} Examples \& Tests\relax }}{83}{table.caption.97}\protected@file@percent }
\newlabel{tab:tests}{{A\unhbox \voidb@x \hbox {-}8}{83}{\mpom {} Examples \& Tests\relax }{table.caption.97}{}}
\citation{bpmpdmex,meszaros1996}
\citation{clp}
\citation{opti}
\@writefile{toc}{\contentsline {section}{Appendix \numberline {B}Optional Packages}{84}{Appendix.1.B}\protected@file@percent }
\newlabel{app:optional_packages}{{B}{84}{Optional Packages}{Appendix.1.B}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.1}BPMPD\_MEX -- MEX interface for BPMPD}{84}{subsection.1.B.1}\protected@file@percent }
\newlabel{app:bpmpd}{{B.1}{84}{BPMPD\_MEX -- MEX interface for BPMPD}{subsection.1.B.1}{}}
\@writefile{brf}{\backcite{bpmpdmex,meszaros1996}{{84}{B.1}{subsection.1.B.1}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.2}{CLP}{} -- COIN-OR Linear Programming}{84}{subsection.1.B.2}\protected@file@percent }
\newlabel{app:clp}{{B.2}{84}{\clp {} -- COIN-OR Linear Programming}{subsection.1.B.2}{}}
\@writefile{brf}{\backcite{clp}{{84}{B.2}{subsection.1.B.2}}}
\citation{glpk}
\citation{opti}
\@writefile{brf}{\backcite{opti}{{85}{B.2}{subsection.1.B.2}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.3}{CPLEX}{} -- High-performance LP, QP, MILP and MIQP Solvers}{85}{subsection.1.B.3}\protected@file@percent }
\newlabel{app:cplex}{{B.3}{85}{\cplex {} -- High-performance LP, QP, MILP and MIQP Solvers}{subsection.1.B.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.4}{GLPK}{} -- GNU Linear Programming Kit}{85}{subsection.1.B.4}\protected@file@percent }
\newlabel{app:glpk}{{B.4}{85}{\glpk {} -- GNU Linear Programming Kit}{subsection.1.B.4}{}}
\@writefile{brf}{\backcite{glpk}{{85}{B.4}{subsection.1.B.4}}}
\@writefile{brf}{\backcite{opti}{{85}{B.4}{subsection.1.B.4}}}
\citation{gurobi}
\citation{ipopt}
\citation{pardiso,pardiso2}
\citation{opti}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.5}{Gurobi}{} -- High-performance LP, QP, MILP and MIQP Solvers}{86}{subsection.1.B.5}\protected@file@percent }
\newlabel{app:gurobi}{{B.5}{86}{\gurobi {} -- High-performance LP, QP, MILP and MIQP Solvers}{subsection.1.B.5}{}}
\@writefile{brf}{\backcite{gurobi}{{86}{B.5}{subsection.1.B.5}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.6}{\sc  Ipopt}{} -- Interior Point Optimizer}{86}{subsection.1.B.6}\protected@file@percent }
\newlabel{app:ipopt}{{B.6}{86}{\ipopt {} -- Interior Point Optimizer}{subsection.1.B.6}{}}
\@writefile{brf}{\backcite{ipopt}{{86}{B.6}{subsection.1.B.6}}}
\@writefile{brf}{\backcite{pardiso, pardiso2}{{86}{B.6}{subsection.1.B.6}}}
\citation{knitro}
\citation{ot,otug}
\@writefile{brf}{\backcite{opti}{{87}{B.6}{subsection.1.B.6}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.7}{Artelys Knitro}{} -- Non-Linear Programming Solver}{87}{subsection.1.B.7}\protected@file@percent }
\newlabel{app:knitro}{{B.7}{87}{\knitro {} -- Non-Linear Programming Solver}{subsection.1.B.7}{}}
\@writefile{brf}{\backcite{knitro}{{87}{B.7}{subsection.1.B.7}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.8}{MOSEK}{} -- High-performance LP, QP, MILP and MIQP Solvers}{87}{subsection.1.B.8}\protected@file@percent }
\newlabel{app:mosek}{{B.8}{87}{\mosek {} -- High-performance LP, QP, MILP and MIQP Solvers}{subsection.1.B.8}{}}
\citation{osqp}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.9}{Optimization Toolbox}{} -- LP, QP, NLP, NLEQ and MILP Solvers}{88}{subsection.1.B.9}\protected@file@percent }
\newlabel{app:ot}{{B.9}{88}{\ot {} -- LP, QP, NLP, NLEQ and MILP Solvers}{subsection.1.B.9}{}}
\@writefile{brf}{\backcite{ot, otug}{{88}{B.9}{subsection.1.B.9}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.10}{OSQP}{} -- Operator Splitting Quadratic Program Solver}{88}{subsection.1.B.10}\protected@file@percent }
\newlabel{app:osqp}{{B.10}{88}{\osqp {} -- Operator Splitting Quadratic Program Solver}{subsection.1.B.10}{}}
\@writefile{brf}{\backcite{osqp}{{88}{B.10}{subsection.1.B.10}}}
\@writefile{toc}{\contentsline {section}{Appendix \numberline {C}Release History}{89}{Appendix.1.C}\protected@file@percent }
\newlabel{app:release_history}{{C}{89}{Release History}{Appendix.1.C}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {C.1}Version 0.7 -- Jun 20, 2019}{89}{subsection.1.C.1}\protected@file@percent }
\newlabel{app:v07}{{C.1}{89}{Version 0.7 -- Jun 20, 2019}{subsection.1.C.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {C.2}Version 0.8 -- Apr 29, 2020 \emph  {(not released publicly)}}{89}{subsection.1.C.2}\protected@file@percent }
\newlabel{app:v08}{{C.2}{89}{Version 0.8 -- Apr 29, 2020 \emph {(not released publicly)}}{subsection.1.C.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {C.3}Version 1.0 -- released May 8, 2020}{91}{subsection.1.C.3}\protected@file@percent }
\newlabel{app:v10}{{C.3}{91}{Version 1.0 -- released May 8, 2020}{subsection.1.C.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {C.4}Version 2.0 -- released Jul 8, 2020}{91}{subsection.1.C.4}\protected@file@percent }
\newlabel{app:v20}{{C.4}{91}{Version 2.0 -- released Jul 8, 2020}{subsection.1.C.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {C.5}Version 2.1 -- released Aug 25, 2020}{93}{subsection.1.C.5}\protected@file@percent }
\newlabel{app:v21}{{C.5}{93}{Version 2.1 -- released Aug 25, 2020}{subsection.1.C.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {C.6}Version 3.0 -- released Sep 24, 2020}{94}{subsection.1.C.6}\protected@file@percent }
\newlabel{app:v30}{{C.6}{94}{Version 3.0 -- released Sep 24, 2020}{subsection.1.C.6}{}}
\bibcite{zimmerman2011}{1}
\bibcite{matpower}{2}
\bibcite{octave}{3}
\bibcite{bsd}{4}
\bibcite{mpom_manual}{5}
\bibcite{wang2007a}{6}
\bibcite{mips_manual}{7}
\bibcite{bpmpdmex}{8}
\bibcite{meszaros1996}{9}
\bibcite{clp}{10}
\bibcite{opti}{11}
\@writefile{toc}{\contentsline {section}{References}{96}{section*.111}\protected@file@percent }
\bibcite{glpk}{12}
\bibcite{gurobi}{13}
\bibcite{ipopt}{14}
\bibcite{pardiso}{15}
\bibcite{pardiso2}{16}
\bibcite{knitro}{17}
\bibcite{ot}{18}
\bibcite{otug}{19}
\bibcite{osqp}{20}
