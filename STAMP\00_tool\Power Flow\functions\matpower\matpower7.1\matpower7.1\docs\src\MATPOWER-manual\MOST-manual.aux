\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyperFirstAtBeginDocument{\AtBeginDocument}
\HyperFirstAtBeginDocument{\ifx\hyper@anchor\@undefined
\global\let\oldcontentsline\contentsline
\gdef\contentsline#1#2#3#4{\oldcontentsline{#1}{#2}{#3}}
\global\let\oldnewlabel\newlabel
\gdef\newlabel#1#2{\newlabelxx{#1}#2}
\gdef\newlabelxx#1#2#3#4#5#6{\oldnewlabel{#1}{{#2}{#3}}}
\AtEndDocument{\ifx\hyper@anchor\@undefined
\let\contentsline\oldcontentsline
\let\newlabel\oldnewlabel
\fi}
\fi}
\global\let\hyper@last\relax 
\gdef\HyperFirstAtBeginDocument#1{#1}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\citation{zimmerman2011,matpower,matpower_manual}
\citation{murillo-sanchez2013a,lamadrid2018}
\citation{zimmerman2011,zimmerman2009}
\citation{matpower_manual}
\citation{bsd}
\@writefile{toc}{\contentsline {section}{\numberline {1}Introduction}{7}{section.1}\protected@file@percent }
\@writefile{brf}{\backcite{zimmerman2011,matpower,matpower_manual}{{7}{1}{section.1}}}
\@writefile{brf}{\backcite{murillo-sanchez2013a,lamadrid2018}{{7}{1}{section.1}}}
\@writefile{brf}{\backcite{zimmerman2011, zimmerman2009}{{7}{1}{section.1}}}
\@writefile{brf}{\backcite{matpower_manual}{{7}{1}{section.1}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {1.1}License and Terms of Use}{7}{subsection.1.1}\protected@file@percent }
\@writefile{brf}{\backcite{bsd}{{7}{1.1}{subsection.1.1}}}
\citation{zimmerman2011}
\citation{murillo-sanchez2013a}
\@writefile{toc}{\contentsline {subsection}{\numberline {1.2}Citing {MOST}{}}{8}{subsection.1.2}\protected@file@percent }
\@writefile{brf}{\backcite{zimmerman2011}{{8}{1.2}{subsection.1.2}}}
\@writefile{brf}{\backcite{murillo-sanchez2013a}{{8}{1.2}{subsection.1.2}}}
\citation{most_manual}
\@writefile{brf}{\backcite{most_manual}{{9}{1.2}{subsection.1.2}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {1.3}{MOST}{} Development}{9}{subsection.1.3}\protected@file@percent }
\newlabel{sec:development}{{1.3}{9}{\most {} Development}{subsection.1.3}{}}
\@writefile{toc}{\contentsline {section}{\numberline {2}Getting Started}{10}{section.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1}System Requirements}{10}{subsection.2.1}\protected@file@percent }
\newlabel{sec:sysreq}{{2.1}{10}{System Requirements}{subsection.2.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2}Installation}{10}{subsection.2.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.3}Running a Simulation}{11}{subsection.2.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {2.3.1}Preparing Input Data}{12}{subsubsection.2.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {2.3.2}Solving the Case}{13}{subsubsection.2.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {2.3.3}Accessing the Results}{14}{subsubsection.2.3.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {2.3.4}Setting Options}{14}{subsubsection.2.3.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.4}Documentation}{14}{subsection.2.4}\protected@file@percent }
\citation{superopf2008,murillo-sanchez2013}
\citation{murillo-sanchez2013a}
\citation{matpower_manual}
\@writefile{toc}{\contentsline {section}{\numberline {3}Background and Overview}{16}{section.3}\protected@file@percent }
\@writefile{brf}{\backcite{superopf2008,murillo-sanchez2013}{{16}{3}{section.3}}}
\@writefile{brf}{\backcite{murillo-sanchez2013a}{{16}{3}{section.3}}}
\@writefile{brf}{\backcite{matpower_manual}{{16}{3}{section.3}}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.1}Continuous Single Period Problems}{16}{subsection.3.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3\unhbox \voidb@x \hbox {-}1}{\ignorespaces MOST Continuous Single-Period Problems}}{17}{figure.3.1}\protected@file@percent }
\newlabel{fig:most_single_period}{{3\unhbox \voidb@x \hbox {-}1}{17}{MOST Continuous Single-Period Problems}{figure.3.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.2}Security}{18}{subsection.3.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3\unhbox \voidb@x \hbox {-}2}{\ignorespaces Secure Dispatch Problem Structure}}{19}{figure.3.2}\protected@file@percent }
\newlabel{fig:secure_dispatch_structure}{{3\unhbox \voidb@x \hbox {-}2}{19}{Secure Dispatch Problem Structure}{figure.3.2}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3\unhbox \voidb@x \hbox {-}3}{\ignorespaces Reserve Structure for Generator\nobreakspace  {}$i$}}{19}{figure.3.3}\protected@file@percent }
\newlabel{fig:reserves_single_base}{{3\unhbox \voidb@x \hbox {-}3}{19}{Reserve Structure for Generator~$i$}{figure.3.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.3}Uncertainty of Demand and Renewable Generation}{20}{subsection.3.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3\unhbox \voidb@x \hbox {-}4}{\ignorespaces Problem Structure with Multiple Base Scenarios}}{20}{figure.3.4}\protected@file@percent }
\newlabel{fig:structure_multi_scenario}{{3\unhbox \voidb@x \hbox {-}4}{20}{Problem Structure with Multiple Base Scenarios}{figure.3.4}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3\unhbox \voidb@x \hbox {-}5}{\ignorespaces Reserve Structure for Generator\nobreakspace  {}$i$ in Period\nobreakspace  {}$t$}}{21}{figure.3.5}\protected@file@percent }
\newlabel{fig:reserves}{{3\unhbox \voidb@x \hbox {-}5}{21}{Reserve Structure for Generator~$i$ in Period~$t$}{figure.3.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.4}Multiple Periods}{21}{subsection.3.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.5}Ramping and Load Following Ramp Reserves}{22}{subsection.3.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.6}Storage and Deferrable Demand}{22}{subsection.3.6}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3\unhbox \voidb@x \hbox {-}6}{\ignorespaces Ramping and Load Following Ramp Reserves}}{23}{figure.3.6}\protected@file@percent }
\newlabel{fig:ramping}{{3\unhbox \voidb@x \hbox {-}6}{23}{Ramping and Load Following Ramp Reserves}{figure.3.6}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3\unhbox \voidb@x \hbox {-}7}{\ignorespaces Storage}}{24}{figure.3.7}\protected@file@percent }
\newlabel{fig:storage}{{3\unhbox \voidb@x \hbox {-}7}{24}{Storage}{figure.3.7}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.7}Linear Time-Varying Dynamical System}{25}{subsection.3.7}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.8}Unit Commitment}{25}{subsection.3.8}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3\unhbox \voidb@x \hbox {-}8}{\ignorespaces Overall Problem Structure}}{26}{figure.3.8}\protected@file@percent }
\newlabel{fig:structure}{{3\unhbox \voidb@x \hbox {-}8}{26}{Overall Problem Structure}{figure.3.8}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3\unhbox \voidb@x \hbox {-}9}{\ignorespaces MOST Mixed Integer and Multi-Period Problems}}{27}{figure.3.9}\protected@file@percent }
\newlabel{fig:most_multi_period}{{3\unhbox \voidb@x \hbox {-}9}{27}{MOST Mixed Integer and Multi-Period Problems}{figure.3.9}{}}
\@writefile{toc}{\contentsline {section}{\numberline {4}Problem Formulation}{28}{section.4}\protected@file@percent }
\newlabel{sec:probformulation}{{4}{28}{Problem Formulation}{section.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1}Nomenclature}{28}{subsection.4.1}\protected@file@percent }
\newlabel{sec:nomenclature}{{4.1}{28}{Nomenclature}{subsection.4.1}{}}
\newlabel{eq:psialpha}{{4.1}{33}{Nomenclature}{equation.4.1}{}}
\newlabel{eq:sdelta}{{4.3}{34}{Nomenclature}{equation.4.3}{}}
\newlabel{eq:expected_dispatch}{{4.4}{34}{Nomenclature}{equation.4.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2}Formulation}{35}{subsection.4.2}\protected@file@percent }
\newlabel{sec:formulation}{{4.2}{35}{Formulation}{subsection.4.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.2.1}Objective Function}{35}{subsubsection.4.2.1}\protected@file@percent }
\newlabel{eq:objfcn1}{{4.5}{35}{Objective Function}{equation.4.5}{}}
\newlabel{eq:mostobjective}{{4.6}{35}{Objective Function}{equation.4.6}{}}
\newlabel{eq:most_energy_cost}{{4.7}{35}{Objective Function}{equation.4.7}{}}
\newlabel{eq:most_zres_cost}{{4.8}{35}{Objective Function}{equation.4.8}{}}
\newlabel{eq:most_cres_cost}{{4.9}{35}{Objective Function}{equation.4.9}{}}
\newlabel{eq:rampcost}{{4.10}{36}{Objective Function}{equation.4.10}{}}
\newlabel{eq:most_rampres_cost}{{4.11}{36}{Objective Function}{equation.4.11}{}}
\newlabel{eq:storagecost}{{4.12}{36}{Objective Function}{equation.4.12}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {4.2.2}Constraints}{36}{subsubsection.4.2.2}\protected@file@percent }
\newlabel{eq:firstopf}{{4.14}{36}{Standard OPF Constraints}{equation.4.14}{}}
\newlabel{eq:reserve_var2}{{4.16}{37}{Standard OPF Constraints}{equation.4.16}{}}
\newlabel{eq:Pg_plus_R2}{{4.17}{37}{Standard OPF Constraints}{equation.4.17}{}}
\newlabel{eq:reserve_req2}{{4.18}{37}{Standard OPF Constraints}{equation.4.18}{}}
\newlabel{eq:firstinterflow}{{4.19}{37}{Standard OPF Constraints}{equation.4.19}{}}
\newlabel{eq:maxresp}{{4.19}{37}{Standard OPF Constraints}{equation.4.19}{}}
\newlabel{eq:maxresm}{{4.20}{37}{Standard OPF Constraints}{equation.4.19}{}}
\newlabel{eq:lastnonintertemp}{{4.22}{37}{Standard OPF Constraints}{equation.4.22}{}}
\newlabel{eq:deltapbounds}{{4.23}{37}{Load-following Ramping Limits and Reserves}{equation.4.23}{}}
\newlabel{eq:deltambounds}{{4.24}{37}{Load-following Ramping Limits and Reserves}{equation.4.24}{}}
\newlabel{eq:rampconstrdef}{{4.25}{37}{Load-following Ramping Limits and Reserves}{equation.4.25}{}}
\newlabel{eq:inter1}{{4.26}{37}{Load-following Ramping Limits and Reserves}{equation.4.26}{}}
\newlabel{eq:rampconstr}{{4.27}{37}{Load-following Ramping Limits and Reserves}{equation.4.27}{}}
\newlabel{eq:storage1st}{{4.33}{38}{Storage Constraints}{equation.4.33}{}}
\newlabel{eq:storagecontingencylast}{{4.36}{38}{Storage Constraints}{equation.4.36}{}}
\newlabel{eq:sftarget}{{4.37}{38}{Storage Constraints}{equation.4.37}{}}
\newlabel{eq:sfequals0}{{4.38}{38}{Storage Constraints}{equation.4.38}{}}
\newlabel{eq:s0bounds}{{4.39}{38}{Storage Constraints}{equation.4.39}{}}
\newlabel{eq:dynbounds}{{4.49}{40}{Linear Time-Varying Dynamical System}{equation.4.49}{}}
\newlabel{eq:dynstate1}{{4.50}{40}{Linear Time-Varying Dynamical System}{equation.4.50}{}}
\newlabel{eq:dynstate2}{{4.51}{40}{Linear Time-Varying Dynamical System}{equation.4.51}{}}
\newlabel{eq:dynout1}{{4.52}{40}{Linear Time-Varying Dynamical System}{equation.4.52}{}}
\newlabel{eq:dynout2}{{4.53}{40}{Linear Time-Varying Dynamical System}{equation.4.53}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3}Probability Weighting of Base and Contingency States}{40}{subsection.4.3}\protected@file@percent }
\newlabel{eq:transmat}{{4.55}{41}{Probability Weighting of Base and Contingency States}{equation.4.55}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.4}Value of Residual Storage}{43}{subsection.4.4}\protected@file@percent }
\newlabel{sec:residualstorage}{{4.4}{43}{Value of Residual Storage}{subsection.4.4}{}}
\newlabel{eq:sf0}{{4.60}{43}{Value of Residual Storage}{equation.4.60}{}}
\newlabel{eq:beta1and2}{{4.61}{43}{Value of Residual Storage}{equation.4.61}{}}
\newlabel{eq:salpha}{{4.62}{43}{Value of Residual Storage}{equation.4.62}{}}
\newlabel{eq:lossk1}{{4.63}{43}{Value of Residual Storage}{equation.4.63}{}}
\newlabel{eq:lossk}{{4.64}{43}{Value of Residual Storage}{equation.4.64}{}}
\newlabel{eq:beta3}{{4.69}{44}{Value of Residual Storage}{equation.4.69}{}}
\newlabel{eq:beta4}{{4.70}{44}{Value of Residual Storage}{equation.4.70}{}}
\newlabel{eq:beta5}{{4.71}{44}{Value of Residual Storage}{equation.4.71}{}}
\newlabel{eq:sf}{{4.74}{45}{Value of Residual Storage}{equation.4.74}{}}
\newlabel{eq:si}{{4.77}{45}{Value of Residual Storage}{equation.4.77}{}}
\newlabel{eq:siall}{{4.81}{46}{Value of Residual Storage}{equation.4.81}{}}
\newlabel{eq:sfall}{{4.82}{46}{Value of Residual Storage}{equation.4.81}{}}
\newlabel{eq:simnall}{{4.83}{46}{Value of Residual Storage}{equation.4.83}{}}
\newlabel{eq:sfmnall}{{4.84}{46}{Value of Residual Storage}{equation.4.83}{}}
\newlabel{eq:LIt}{{4.85}{47}{Value of Residual Storage}{equation.4.85}{}}
\newlabel{eq:LFt}{{4.86}{47}{Value of Residual Storage}{equation.4.85}{}}
\newlabel{eq:Mtg}{{4.87}{47}{Value of Residual Storage}{equation.4.85}{}}
\newlabel{eq:Mth}{{4.88}{47}{Value of Residual Storage}{equation.4.85}{}}
\newlabel{eq:Ntg}{{4.89}{47}{Value of Residual Storage}{equation.4.85}{}}
\newlabel{eq:Nth}{{4.90}{47}{Value of Residual Storage}{equation.4.85}{}}
\newlabel{eq:SFntj}{{4.91}{47}{Value of Residual Storage}{equation.4.91}{}}
\newlabel{eq:SFtjk}{{4.92}{47}{Value of Residual Storage}{equation.4.92}{}}
\newlabel{eq:sfend}{{4.93}{47}{Value of Residual Storage}{equation.4.93}{}}
\newlabel{eq:singleprice}{{4.95}{48}{Value of Residual Storage}{equation.4.95}{}}
\@writefile{lot}{\contentsline {table}{\numberline {4\unhbox \voidb@x \hbox {-}1}{\ignorespaces Five Price Model}}{48}{table.4.1}\protected@file@percent }
\newlabel{tab:fivepricemodel}{{4\unhbox \voidb@x \hbox {-}1}{48}{Five Price Model}{table.4.1}{}}
\newlabel{eq:vs}{{4.96}{49}{Value of Residual Storage}{equation.4.96}{}}
\newlabel{eq:fs}{{4.104}{49}{Value of Residual Storage}{equation.4.104}{}}
\newlabel{eq:Ctsd}{{4.107}{50}{Value of Residual Storage}{equation.4.107}{}}
\@writefile{toc}{\contentsline {section}{\numberline {5}{\tt  most}}{51}{section.5}\protected@file@percent }
\newlabel{sec:mostfcn}{{5}{51}{\tt most}{section.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.1}Input Data}{51}{subsection.5.1}\protected@file@percent }
\newlabel{sec:inputdata}{{5.1}{51}{Input Data}{subsection.5.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.1.1}{\tt  mpc} -- {\sc  Matpower}{} Case}{51}{subsubsection.5.1.1}\protected@file@percent }
\newlabel{sec:mpc}{{5.1.1}{51}{{\tt mpc} -- \matpower {} Case}{subsubsection.5.1.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5\unhbox \voidb@x \hbox {-}1}{\ignorespaces Assembling the {{MOST}{} Data struct}{}}}{52}{figure.5.1}\protected@file@percent }
\newlabel{fig:loadmd}{{5\unhbox \voidb@x \hbox {-}1}{52}{Assembling the \md {}}{figure.5.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.1.2}{\tt  transmat} -- Transition Probability Matrices}{53}{subsubsection.5.1.2}\protected@file@percent }
\newlabel{sec:transmat}{{5.1.2}{53}{{\tt transmat} -- Transition Probability Matrices}{subsubsection.5.1.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.1.3}{\tt  xgd} -- Extra Generator Data ({\tt  xGenData})}{53}{subsubsection.5.1.3}\protected@file@percent }
\newlabel{sec:xgd}{{5.1.3}{53}{{\tt xgd} -- Extra Generator Data ({\tt xGenData})}{subsubsection.5.1.3}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}1}{\ignorespaces Fields\TPToverlap {\textsuperscript  {*}} of {\relsize  {-0.5}{\tt  {{xGenData}}}} struct ({\relsize  {-0.5}{\tt  {{xgd}}}})}}{54}{table.5.1}\protected@file@percent }
\newlabel{tab:xgd}{{5\unhbox \voidb@x \hbox {-}1}{54}{Fields\tnote {*} of \code {xGenData} struct (\code {xgd})}{table.5.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.1.4}{\tt  sd} -- Storage Data ({\tt  StorageData})}{55}{subsubsection.5.1.4}\protected@file@percent }
\newlabel{sec:sd}{{5.1.4}{55}{{\tt sd} -- Storage Data ({\tt StorageData})}{subsubsection.5.1.4}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.1.5}{\tt  contab} -- Contingency Table}{55}{subsubsection.5.1.5}\protected@file@percent }
\newlabel{sec:contab}{{5.1.5}{55}{{\tt contab} -- Contingency Table}{subsubsection.5.1.5}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}2}{\ignorespaces Fields\TPToverlap {\textsuperscript  {*}} of {\relsize  {-0.5}{\tt  {{StorageData}}}} struct ({\relsize  {-0.5}{\tt  {{sd}}}})}}{56}{table.5.2}\protected@file@percent }
\newlabel{tab:sd}{{5\unhbox \voidb@x \hbox {-}2}{56}{Fields\tnote {*} of \code {StorageData} struct (\code {sd})}{table.5.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.1.6}{\tt  profiles} -- Profiles for Time-Varying Parameters}{57}{subsubsection.5.1.6}\protected@file@percent }
\newlabel{sec:profiles}{{5.1.6}{57}{{\tt profiles} -- Profiles for Time-Varying Parameters}{subsubsection.5.1.6}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}3}{\ignorespaces Fields of Profle Struct ({\relsize  {-0.5}{\tt  {{profile}}}})}}{57}{table.5.3}\protected@file@percent }
\newlabel{tab:profile}{{5\unhbox \voidb@x \hbox {-}3}{57}{Fields of Profle Struct (\code {profile})}{table.5.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.2}{MOST}{} Options}{58}{subsection.5.2}\protected@file@percent }
\newlabel{sec:mostoptions}{{5.2}{58}{\most {} Options}{subsection.5.2}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}4}{\ignorespaces {MOST}{} Run Options}}{58}{table.5.4}\protected@file@percent }
\newlabel{tab:mostrunoptions}{{5\unhbox \voidb@x \hbox {-}4}{58}{\most {} Run Options}{table.5.4}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}5}{\ignorespaces {MOST}{} Model Options}}{59}{table.5.5}\protected@file@percent }
\newlabel{tab:mostmodeloptions}{{5\unhbox \voidb@x \hbox {-}5}{59}{\most {} Model Options}{table.5.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.3}{{MOST}{} Data struct}{}}{60}{subsection.5.3}\protected@file@percent }
\newlabel{sec:md}{{5.3}{60}{\md {}}{subsection.5.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.3.1}Input Data}{60}{subsubsection.5.3.1}\protected@file@percent }
\newlabel{sec:mdinputdata}{{5.3.1}{60}{Input Data}{subsubsection.5.3.1}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}6}{\ignorespaces Input Data Fields of {\relsize  {-0.5}{\tt  {{md}}}}}}{61}{table.5.6}\protected@file@percent }
\newlabel{tab:md_inputs}{{5\unhbox \voidb@x \hbox {-}6}{61}{Input Data Fields of \code {md}}{table.5.6}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}7}{\ignorespaces Additional Input Data Fields of {\relsize  {-0.5}{\tt  {{md}}}}}}{62}{table.5.7}\protected@file@percent }
\newlabel{tab:md_inputs2}{{5\unhbox \voidb@x \hbox {-}7}{62}{Additional Input Data Fields of \code {md}}{table.5.7}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}8}{\ignorespaces Fields of Offer struct {\relsize  {-0.5}{\tt  {{md.offer(t)}}}}}}{63}{table.5.8}\protected@file@percent }
\newlabel{tab:md_inputoffer}{{5\unhbox \voidb@x \hbox {-}8}{63}{Fields of Offer struct \code {md.offer(t)}}{table.5.8}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}9}{\ignorespaces Input Fields of {\relsize  {-0.5}{\tt  {{md.Storage}}}}}}{64}{table.5.9}\protected@file@percent }
\newlabel{tab:md_inputstorage}{{5\unhbox \voidb@x \hbox {-}9}{64}{Input Fields of \code {md.Storage}}{table.5.9}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {5.3.2}Output Data}{65}{subsubsection.5.3.2}\protected@file@percent }
\newlabel{sec:mdoutputdata}{{5.3.2}{65}{Output Data}{subsubsection.5.3.2}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}10}{\ignorespaces Output Data Fields of {\relsize  {-0.5}{\tt  {{md}}}}}}{66}{table.5.10}\protected@file@percent }
\newlabel{tab:md_outputs}{{5\unhbox \voidb@x \hbox {-}10}{66}{Output Data Fields of \code {md}}{table.5.10}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}11}{\ignorespaces Fields of Index struct {\relsize  {-0.5}{\tt  {{md.idx}}}}}}{67}{table.5.11}\protected@file@percent }
\newlabel{tab:md_inputidx}{{5\unhbox \voidb@x \hbox {-}11}{67}{Fields of Index struct \code {md.idx}}{table.5.11}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}12}{\ignorespaces Fields of QP struct {\relsize  {-0.5}{\tt  {{md.QP}}}}}}{68}{table.5.12}\protected@file@percent }
\newlabel{tab:md_qp}{{5\unhbox \voidb@x \hbox {-}12}{68}{Fields of QP struct \code {md.QP}}{table.5.12}{}}
\@writefile{lot}{\contentsline {table}{\numberline {5\unhbox \voidb@x \hbox {-}13}{\ignorespaces Fields of Results struct {\relsize  {-0.5}{\tt  {{md.results}}}}}}{69}{table.5.13}\protected@file@percent }
\newlabel{tab:md_results}{{5\unhbox \voidb@x \hbox {-}13}{69}{Fields of Results struct \code {md.results}}{table.5.13}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.4}Additional Considerations}{70}{subsection.5.4}\protected@file@percent }
\newlabel{sec:mostmisc}{{5.4}{70}{Additional Considerations}{subsection.5.4}{}}
\@writefile{toc}{\contentsline {section}{\numberline {6}Additional Functions}{71}{section.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {6.1}\tt  addgen2mpc}{71}{subsection.6.1}\protected@file@percent }
\newlabel{sec:addgen2mpc}{{6.1}{71}{\tt addgen2mpc}{subsection.6.1}{}}
\@writefile{lot}{\contentsline {table}{\numberline {6\unhbox \voidb@x \hbox {-}1}{\ignorespaces Typical Generator Types}}{71}{table.6.1}\protected@file@percent }
\newlabel{tab:gen_types}{{6\unhbox \voidb@x \hbox {-}1}{71}{Typical Generator Types}{table.6.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.2}\tt  addstorage}{72}{subsection.6.2}\protected@file@percent }
\newlabel{sec:addstorage}{{6.2}{72}{\tt addstorage}{subsection.6.2}{}}
\@writefile{lot}{\contentsline {table}{\numberline {6\unhbox \voidb@x \hbox {-}2}{\ignorespaces Fields of {\relsize  {-0.5}{\tt  {{StorageUnitData}}}} struct ({\relsize  {-0.5}{\tt  {{storage}}}})}}{72}{table.6.2}\protected@file@percent }
\newlabel{tab:storage_unit_data}{{6\unhbox \voidb@x \hbox {-}2}{72}{Fields of \code {StorageUnitData} struct (\code {storage})}{table.6.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.3}\tt  addwind}{72}{subsection.6.3}\protected@file@percent }
\newlabel{sec:addwind}{{6.3}{72}{\tt addwind}{subsection.6.3}{}}
\@writefile{lot}{\contentsline {table}{\numberline {6\unhbox \voidb@x \hbox {-}3}{\ignorespaces Fields of {\relsize  {-0.5}{\tt  {{WindUnitData}}}} struct ({\relsize  {-0.5}{\tt  {{wind}}}})}}{73}{table.6.3}\protected@file@percent }
\newlabel{tab:wind_unit_data}{{6\unhbox \voidb@x \hbox {-}3}{73}{Fields of \code {WindUnitData} struct (\code {wind})}{table.6.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.4}\tt  apply\_profile}{73}{subsection.6.4}\protected@file@percent }
\newlabel{sec:apply_profile}{{6.4}{73}{\tt apply\_profile}{subsection.6.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.5}\tt  filter\_ramp\_transitions}{73}{subsection.6.5}\protected@file@percent }
\newlabel{sec:filter_ramp_transitions}{{6.5}{73}{\tt filter\_ramp\_transitions}{subsection.6.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.6}\tt  getprofiles}{74}{subsection.6.6}\protected@file@percent }
\newlabel{sec:getprofiles}{{6.6}{74}{\tt getprofiles}{subsection.6.6}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.7}\tt  idx\_profile}{74}{subsection.6.7}\protected@file@percent }
\newlabel{sec:idx_profile}{{6.7}{74}{\tt idx\_profile}{subsection.6.7}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.8}\tt  loadgenericdata}{74}{subsection.6.8}\protected@file@percent }
\newlabel{sec:loadgenericdata}{{6.8}{74}{\tt loadgenericdata}{subsection.6.8}{}}
\@writefile{lot}{\contentsline {table}{\numberline {6\unhbox \voidb@x \hbox {-}4}{\ignorespaces Constants Defined by {\relsize  {-0.5}{\tt  {{idx\_profile}}}}}}{75}{table.6.4}\protected@file@percent }
\newlabel{tab:idx_profile}{{6\unhbox \voidb@x \hbox {-}4}{75}{Constants Defined by \code {idx\_profile}}{table.6.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.9}\tt  loadmd}{75}{subsection.6.9}\protected@file@percent }
\newlabel{sec:loadmd}{{6.9}{75}{\tt loadmd}{subsection.6.9}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.10}\tt  loadstoragedata}{76}{subsection.6.10}\protected@file@percent }
\newlabel{sec:loadstoragedata}{{6.10}{76}{\tt loadstoragedata}{subsection.6.10}{}}
\@writefile{lot}{\contentsline {table}{\numberline {6\unhbox \voidb@x \hbox {-}5}{\ignorespaces Fields of {\relsize  {-0.5}{\tt  {{StorageDataTable}}}} struct ({\relsize  {-0.5}{\tt  {{sd\_table}}}})}}{76}{table.6.5}\protected@file@percent }
\newlabel{tab:sd_table}{{6\unhbox \voidb@x \hbox {-}5}{76}{Fields of \code {StorageDataTable} struct (\code {sd\_table})}{table.6.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.11}\tt  loadxgendata}{77}{subsection.6.11}\protected@file@percent }
\newlabel{sec:loadxgendata}{{6.11}{77}{\tt loadxgendata}{subsection.6.11}{}}
\@writefile{lot}{\contentsline {table}{\numberline {6\unhbox \voidb@x \hbox {-}6}{\ignorespaces Fields of {\relsize  {-0.5}{\tt  {{xGenDataTable}}}} struct ({\relsize  {-0.5}{\tt  {{xgd\_table}}}})}}{78}{table.6.6}\protected@file@percent }
\newlabel{tab:xgd_table}{{6\unhbox \voidb@x \hbox {-}6}{78}{Fields of \code {xGenDataTable} struct (\code {xgd\_table})}{table.6.6}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.12}\tt  most\_summary}{79}{subsection.6.12}\protected@file@percent }
\newlabel{sec:most_summary}{{6.12}{79}{\tt most\_summary}{subsection.6.12}{}}
\@writefile{lot}{\contentsline {table}{\numberline {6\unhbox \voidb@x \hbox {-}7}{\ignorespaces Fields of {\relsize  {-0.5}{\tt  {{most\_summary}}}} struct ({\relsize  {-0.5}{\tt  {{ms}}}})}}{79}{table.6.7}\protected@file@percent }
\newlabel{tab:most_summary}{{6\unhbox \voidb@x \hbox {-}7}{79}{Fields of \code {most\_summary} struct (\code {ms})}{table.6.7}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {6.13}\tt  mostver}{80}{subsection.6.13}\protected@file@percent }
\newlabel{sec:mostver}{{6.13}{80}{\tt mostver}{subsection.6.13}{}}
\@writefile{toc}{\contentsline {section}{\numberline {7}Tutorial Examples}{81}{section.7}\protected@file@percent }
\newlabel{sec:tutorial}{{7}{81}{Tutorial Examples}{section.7}{}}
\@writefile{lot}{\contentsline {table}{\numberline {7\unhbox \voidb@x \hbox {-}1}{\ignorespaces Summary of Tutorial Example System Data}}{81}{table.7.1}\protected@file@percent }
\newlabel{tab:threebus}{{7\unhbox \voidb@x \hbox {-}1}{81}{Summary of Tutorial Example System Data}{table.7.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}1}{\ignorespaces Tutorial Example System}}{82}{figure.7.1}\protected@file@percent }
\newlabel{fig:threebus}{{7\unhbox \voidb@x \hbox {-}1}{82}{Tutorial Example System}{figure.7.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {7.1}Single Period Problems}{82}{subsection.7.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {7.1.1}Example 1 -- Deterministic Economic Dispatch}{82}{subsubsection.7.1.1}\protected@file@percent }
\newlabel{sec:tutex1}{{7.1.1}{82}{Example 1 -- Deterministic Economic Dispatch}{subsubsection.7.1.1}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {7.1.2}Example 2 -- Deterministic DC OPF}{84}{subsubsection.7.1.2}\protected@file@percent }
\newlabel{sec:tutex2}{{7.1.2}{84}{Example 2 -- Deterministic DC OPF}{subsubsection.7.1.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {7.1.3}Example 3 -- Deterministic DC OPF with Binary Commitment}{85}{subsubsection.7.1.3}\protected@file@percent }
\newlabel{sec:tutex3}{{7.1.3}{85}{Example 3 -- Deterministic DC OPF with Binary Commitment}{subsubsection.7.1.3}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {7.1.4}Example 4 -- Secure and Stochastic DC OPF}{86}{subsubsection.7.1.4}\protected@file@percent }
\newlabel{sec:tutex4}{{7.1.4}{86}{Example 4 -- Secure and Stochastic DC OPF}{subsubsection.7.1.4}{}}
\newlabel{sec:secdcopf}{{7.1.4}{86}{Secure DC OPF - with contingencies}{section*.10}{}}
\newlabel{sec:stochdcopf}{{7.1.4}{87}{Stochastic DC OPF - with renewable uncertainty}{section*.11}{}}
\newlabel{sec:ssdcopf}{{7.1.4}{88}{Secure Stochastic DC OPF}{section*.12}{}}
\newlabel{sec:ssdcopfuc}{{7.1.4}{88}{Secure Stochastic DC OPF with Binary Commitment}{section*.13}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {7.2}Multiperiod Problems}{89}{subsection.7.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {7.2.1}Example 5 -- Deterministic Multiperiod OPF}{89}{subsubsection.7.2.1}\protected@file@percent }
\newlabel{sec:tutex5}{{7.2.1}{89}{Example 5 -- Deterministic Multiperiod OPF}{subsubsection.7.2.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}2}{\ignorespaces Example Load and Wind Profiles}}{90}{figure.7.2}\protected@file@percent }
\newlabel{fig:load_wind_profiles}{{7\unhbox \voidb@x \hbox {-}2}{90}{Example Load and Wind Profiles}{figure.7.2}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {7.2.2}Example 6 -- Deterministic Unit Commitment}{91}{subsubsection.7.2.2}\protected@file@percent }
\newlabel{sec:tutex6}{{7.2.2}{91}{Example 6 -- Deterministic Unit Commitment}{subsubsection.7.2.2}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}3}{\ignorespaces Deterministic UC : Base Case with No Network}}{93}{figure.7.3}\protected@file@percent }
\newlabel{fig:uc_ex_1}{{7\unhbox \voidb@x \hbox {-}3}{93}{Deterministic UC : Base Case with No Network}{figure.7.3}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}4}{\ignorespaces Deterministic UC : Add DC Network Model}}{94}{figure.7.4}\protected@file@percent }
\newlabel{fig:uc_ex_2}{{7\unhbox \voidb@x \hbox {-}4}{94}{Deterministic UC : Add DC Network Model}{figure.7.4}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}5}{\ignorespaces Deterministic UC : Add Startup and Shutdown Costs}}{95}{figure.7.5}\protected@file@percent }
\newlabel{fig:uc_ex_3}{{7\unhbox \voidb@x \hbox {-}5}{95}{Deterministic UC : Add Startup and Shutdown Costs}{figure.7.5}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}6}{\ignorespaces Deterministic UC : Add Min Up/Down Time Constraints}}{97}{figure.7.6}\protected@file@percent }
\newlabel{fig:uc_ex_4}{{7\unhbox \voidb@x \hbox {-}6}{97}{Deterministic UC : Add Min Up/Down Time Constraints}{figure.7.6}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}7}{\ignorespaces Deterministic UC : Add Ramp Constraints and Ramp Reserve Costs}}{98}{figure.7.7}\protected@file@percent }
\newlabel{fig:uc_ex_5}{{7\unhbox \voidb@x \hbox {-}7}{98}{Deterministic UC : Add Ramp Constraints and Ramp Reserve Costs}{figure.7.7}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}8}{\ignorespaces Deterministic UC : Add Storage}}{99}{figure.7.8}\protected@file@percent }
\newlabel{fig:uc_ex_6}{{7\unhbox \voidb@x \hbox {-}8}{99}{Deterministic UC : Add Storage}{figure.7.8}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {7.2.3}Example 7 -- Secure Stochastic Unit Commitment}{100}{subsubsection.7.2.3}\protected@file@percent }
\newlabel{sec:tutex7}{{7.2.3}{100}{Example 7 -- Secure Stochastic Unit Commitment}{subsubsection.7.2.3}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}9}{\ignorespaces Example Wind Profiles, Individual Trajectories}}{101}{figure.7.9}\protected@file@percent }
\newlabel{fig:wind_profile_1}{{7\unhbox \voidb@x \hbox {-}9}{101}{Example Wind Profiles, Individual Trajectories}{figure.7.9}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}10}{\ignorespaces Stochastic UC : Individual Trajectories}}{102}{figure.7.10}\protected@file@percent }
\newlabel{fig:suc_ex_2}{{7\unhbox \voidb@x \hbox {-}10}{102}{Stochastic UC : Individual Trajectories}{figure.7.10}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}11}{\ignorespaces Example Wind Profiles, Full Transition Probabilities}}{103}{figure.7.11}\protected@file@percent }
\newlabel{fig:wind_profile_2}{{7\unhbox \voidb@x \hbox {-}11}{103}{Example Wind Profiles, Full Transition Probabilities}{figure.7.11}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}12}{\ignorespaces Stochastic UC : Full Transition Probabilities}}{104}{figure.7.12}\protected@file@percent }
\newlabel{fig:suc_ex_3}{{7\unhbox \voidb@x \hbox {-}12}{104}{Stochastic UC : Full Transition Probabilities}{figure.7.12}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}13}{\ignorespaces Secure Stochastic UC : Full Transition Probabilities + Contingencies}}{105}{figure.7.13}\protected@file@percent }
\newlabel{fig:suc_ex_4}{{7\unhbox \voidb@x \hbox {-}13}{105}{Secure Stochastic UC : Full Transition Probabilities + Contingencies}{figure.7.13}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7\unhbox \voidb@x \hbox {-}14}{\ignorespaces Secure Stochastic UC with Storage}}{106}{figure.7.14}\protected@file@percent }
\newlabel{fig:suc_ex_5}{{7\unhbox \voidb@x \hbox {-}14}{106}{Secure Stochastic UC with Storage}{figure.7.14}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {7.2.4}Dynamical System Constraint Example}{106}{subsubsection.7.2.4}\protected@file@percent }
\newlabel{sec:ds_ex}{{7.2.4}{106}{Dynamical System Constraint Example}{subsubsection.7.2.4}{}}
\@writefile{toc}{\contentsline {section}{\numberline {8}Acknowledgments}{107}{section.8}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{Appendix \numberline {A}{MOST}{} Files and Functions}{108}{Appendix.1.A}\protected@file@percent }
\newlabel{app:functions}{{A}{108}{\most {} Files and Functions}{Appendix.1.A}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {A.1}{MOST}{} Documentation Files}{108}{subsection.1.A.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}1}{\ignorespaces {MOST}{} Documentation Files}}{108}{table.1.A.1}\protected@file@percent }
\newlabel{tab:mostdocfiles}{{A\unhbox \voidb@x \hbox {-}1}{108}{\most {} Documentation Files}{table.1.A.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {A.2}{MOST}{} Functions}{109}{subsection.1.A.2}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}2}{\ignorespaces {MOST}{} Functions}}{109}{table.1.A.2}\protected@file@percent }
\newlabel{tab:mostsw}{{A\unhbox \voidb@x \hbox {-}2}{109}{\most {} Functions}{table.1.A.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {A.3}Automated Test Suite}{110}{subsection.1.A.3}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}3}{\ignorespaces {MOST}{} Test and Example Data}}{110}{table.1.A.3}\protected@file@percent }
\newlabel{tab:mosttestdata}{{A\unhbox \voidb@x \hbox {-}3}{110}{\most {} Test and Example Data}{table.1.A.3}{}}
\@writefile{lot}{\contentsline {table}{\numberline {A\unhbox \voidb@x \hbox {-}4}{\ignorespaces {MOST}{} Tests}}{111}{table.1.A.4}\protected@file@percent }
\newlabel{tab:mosttests}{{A\unhbox \voidb@x \hbox {-}4}{111}{\most {} Tests}{table.1.A.4}{}}
\@writefile{toc}{\contentsline {section}{Appendix \numberline {B}Release History}{112}{Appendix.1.B}\protected@file@percent }
\newlabel{app:release_history}{{B}{112}{Release History}{Appendix.1.B}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.1}Version 1.0 -- released Dec 16, 2016}{112}{subsection.1.B.1}\protected@file@percent }
\newlabel{app:v10}{{B.1}{112}{Version 1.0 -- released Dec 16, 2016}{subsection.1.B.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.2}Version 1.0.1 -- released Oct 30, 2018}{112}{subsection.1.B.2}\protected@file@percent }
\newlabel{app:v101}{{B.2}{112}{Version 1.0.1 -- released Oct 30, 2018}{subsection.1.B.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.3}Version 1.0.2 -- released Jun 20, 2019}{113}{subsection.1.B.3}\protected@file@percent }
\newlabel{app:v102}{{B.3}{113}{Version 1.0.2 -- released Jun 20, 2019}{subsection.1.B.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {B.4}Version 1.1 -- released Oct 8, 2020}{114}{subsection.1.B.4}\protected@file@percent }
\newlabel{app:v11}{{B.4}{114}{Version 1.1 -- released Oct 8, 2020}{subsection.1.B.4}{}}
\bibcite{zimmerman2011}{1}
\bibcite{matpower}{2}
\bibcite{matpower_manual}{3}
\bibcite{murillo-sanchez2013a}{4}
\bibcite{lamadrid2018}{5}
\bibcite{zimmerman2009}{6}
\bibcite{bsd}{7}
\bibcite{most_manual}{8}
\bibcite{superopf2008}{9}
\@writefile{toc}{\contentsline {section}{References}{115}{section*.34}\protected@file@percent }
\bibcite{murillo-sanchez2013}{10}
