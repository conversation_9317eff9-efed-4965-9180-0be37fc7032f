dist: xenial

language: generic

addons:
  apt:
    sources:
    - sourceline: 'ppa:octave/stable'   ## without this, it's octave 4.0.0
    packages:
    - liboctave-dev
    - octave        # octave 4.2.2
    update: true

install:
# - sudo apt-add-repository -y ppa:octave/stable
# - sudo apt-get update -qq
# - sudo apt-get install -qq octave liboctave-dev
- git clone --depth=50 https://github.com/MATPOWER/mptest.git $TRAVIS_BUILD_DIR/mptest
- git clone --depth=50 https://github.com/MATPOWER/mips.git $TRAVIS_BUILD_DIR/mips
- git clone --depth=50 https://github.com/MATPOWER/matpower.git $TRAVIS_BUILD_DIR/matpower

env:
  global:
    - MLL_PATH=$TRAVIS_BUILD_DIR:$TRAVIS_BUILD_DIR/tests:$TRAVIS_BUILD_DIR/examples
    - MATPOWER_PATH=$TRAVIS_BUILD_DIR/matpower

script:
# install MATPOWER
- octave-cli --no-gui --eval ver
- octave-cli --no-gui -p $MATPOWER_PATH --eval "install_matpower(1,1,1)"
- octave-cli --no-gui --eval mpver

## run tests
- octave-cli --no-gui -p $MLL_PATH --eval "test_mll_main(0,1)"
