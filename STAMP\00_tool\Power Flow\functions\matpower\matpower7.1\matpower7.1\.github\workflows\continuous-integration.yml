name: CI

on: [push]

jobs:
  build:
    strategy:
      matrix:
        os: [macos-latest, ubuntu-16.04]

    runs-on: ${{ matrix.os }}

    steps: 
    - uses: actions/checkout@v1
    - name: Initialize Package Manager (Linux)
      if: startsWith(matrix.os, 'ubuntu')
      run: |
        sudo add-apt-repository ppa:octave/stable
        sudo apt-get update -qq
    - name: Initialize Package Manager (Mac)
      if: startsWith(matrix.os, 'macos')
      run: |
        brew update
        brew install bash
        brew install pkg-config
    - name: Install GNU Octave / IPOPT (Linux)
      if: startsWith(matrix.os, 'ubuntu')
      run: |
        sudo apt-get install -y -qq coinor-libipopt-dev liboctave-dev octave
        octave-cli --no-gui --eval ver
        echo "::set-env name=OCTAVE_VER::`octave-cli --no-gui --eval "fprintf('%s', ver('octave').Version)"`"
    - name: Install GNU Octave (Mac)
      if: startsWith(matrix.os, 'macos')
      run: |
        brew install octave
        octave-cli --no-gui --eval ver
        echo "::set-env name=OCTAVE_VER::`octave-cli --no-gui --eval "fprintf('%s', ver('octave').Version)"`"
    - name: Build OSQP
      run: |
        mkdir $HOME/build
        cd $HOME/build
        git clone --recursive https://github.com/oxfordcontrol/osqp
        git clone --recurse-submodules https://github.com/oxfordcontrol/osqp-matlab
        mkdir $HOME/build/osqp/build
        mkdir $HOME/install
        cd $HOME/build/osqp/build
        cmake -DCMAKE_INSTALL_PREFIX=$HOME/install -G "Unix Makefiles" ..
        cmake --build .
        cmake --install .
        ln -s /usr/include/octave-${OCTAVE_VER}/octave/Matrix.h $HOME/install/include/osqp/matrix.h
        ls -al $HOME/install/include/osqp
        cd $HOME/build/osqp-matlab
        octave-cli --no-gui --eval "mex -I$HOME/install/include/osqp  -I$HOME/install/include/qdldl -I$HOME/build/osqp/lin_sys/direct/qdldl osqp_mex.cpp $HOME/install/lib/libosqp.a"
        ls -al
        echo "::set-env name=OSQP_PATH::$HOME/build/osqp-matlab"
    - name: Build IPOPT from source (Mac)
      if: startsWith(matrix.os, 'macos')
      run: |
        git clone https://www.github.com/coin-or/coinbrew
        cd coinbrew
        ./coinbrew fetch Ipopt --no-prompt
        ./coinbrew build Ipopt --prefix=$HOME/install --test --no-prompt
        ./coinbrew install Ipopt
    - name: Build IPOPT MEX interface
      env:
        IPOPT_VER: 3.11.9
      run: |
        export PKG_CONFIG_PATH=$HOME/install/lib/pkgconfig
        curl -SL https://github.com/coin-or/Ipopt/archive/releases/${IPOPT_VER}.tar.gz | tar -xzC $HOME/build
        mv $HOME/build/Ipopt-releases-${IPOPT_VER}/Ipopt/contrib/MatlabInterface $HOME/build/ipopt
        mv $GITHUB_WORKSPACE/.travis/Makefile $HOME/build/ipopt/src
        make -C $HOME/build/ipopt/src
        mv $HOME/build/ipopt/src/*.mex $HOME/build/ipopt/
    - name: Install IPOPT Octave interface
      run: |
        octave-cli --no-gui --eval ver
        octave-cli --no-gui --eval "addpath('$HOME/build/ipopt'); savepath"
    - name: Fix OSQP for Octave < 5.x (Linux)
      if: startsWith(matrix.os, 'ubuntu')
      run: echo "::set-env name=OSQP_PATH::${GITHUB_WORKSPACE}/mp-opt-model/.github/osqp:${HOME}/build/osqp-matlab"
    - name: Install OSQP Octave interface
      run: octave-cli --no-gui --eval "addpath('${OSQP_PATH}'); savepath"
    - name: Install MATPOWER
      run: |
        octave-cli --no-gui --eval ver
        octave-cli --no-gui --eval "install_matpower(1,1,1)"
        octave-cli --no-gui --eval mpver
    - name: Print GLPK/IPOPT version numbers
      run: |
        octave-cli --no-gui --eval "qps_glpk( [],[1; 1],[1 1],[2],[2],[1; 1],[1; 1],[1; 1],struct('verbose', 3));"
        octave-cli --no-gui --eval "qps_ipopt([],[1; 1],[1 1],[2],[2],[1; 1],[1; 1],[1; 1],struct('verbose', 2));"
    - name: Test for successful Ipopt/OSQP installs
      run: |
        octave-cli --no-gui --eval "if ~have_feature('ipopt'), exit(1); end"
        octave-cli --no-gui --eval "if ~have_feature('osqp'), exit(1); end"
    - name: Test MP-Test
      run: octave-cli --no-gui --eval "test_mptest(0,1)"
    - name: Test MIPS
      run: octave-cli --no-gui --eval "test_mips(0,1)"
    - name: Test MP-Opt-Model
      run: octave-cli --no-gui --eval "test_mp_opt_model(0,1)"
    - name: Test MATPOWER
      run: octave-cli --no-gui --eval "test_matpower(0,1)"
    - name: Test MOST
      run: octave-cli --no-gui --eval "test_most(0,1)"
